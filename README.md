# OmniEmbodied

**OmniEmbodied** is a comprehensive simulation and evaluation platform for embodied AI agents. It provides a flexible framework for creating, training, and evaluating intelligent agents in simulated environments with complex multi-agent scenarios and task-oriented interactions.

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)
[![Documentation Status](https://img.shields.io/badge/docs-latest-blue)](https://omniembodied.readthedocs.io/)

## 🎯 Key Features

- **🤖 Multi-Agent Support**: Both single-agent and multi-agent scenarios with complex collaboration patterns
- **🏗️ Flexible Architecture**: Modular design allowing easy extension and customization
- **📊 Comprehensive Evaluation**: Built-in evaluation framework with detailed analytics
- **🔧 Rich Configuration**: Extensive YAML-based configuration management
- **💬 LLM Integration**: Seamless integration with OpenAI, Anthropic, and other language models
- **📈 Data Generation**: Automated tools for creating custom training and evaluation datasets

## 🚀 Quick Start

### Installation

```bash
git clone https://github.com/omniembodied/OmniEmbodied.git
cd OmniEmbodied
pip install -e .
pip install -e OmniSimulator/
```

### Your First Simulation

```bash
# Set your API key
export OPENAI_API_KEY="your-api-key-here"

# Run a basic simulation
python examples/single_agent_example.py
```

### Basic Usage

```python
from OmniSimulator.core.engine import SimulationEngine

# Initialize and run simulation
engine = SimulationEngine()
results = engine.run_simulation(scenario_id="00001")
print(f"Success rate: {results['success_rate']:.2%}")
```

## 📚 Documentation

**Complete documentation is available at: [https://omniembodied.readthedocs.io/](https://omniembodied.readthedocs.io/)**

- **[Installation Guide](https://omniembodied.readthedocs.io/en/latest/installation.html)** - Detailed setup instructions
- **[Quick Start](https://omniembodied.readthedocs.io/en/latest/quickstart.html)** - Get running in minutes
- **[User Guide](https://omniembodied.readthedocs.io/en/latest/user_guide/index.html)** - Comprehensive usage guide
- **[API Reference](https://omniembodied.readthedocs.io/en/latest/api/index.html)** - Complete API documentation
- **[Examples](https://omniembodied.readthedocs.io/en/latest/examples/index.html)** - Practical tutorials and examples

### Local Documentation

To build and serve documentation locally:

```bash
cd docs/
pip install -r requirements.txt
make html
make serve  # Serves at http://localhost:8000
```

## 🏗️ Architecture

```
OmniEmbodied/
├── OmniSimulator/          # Core simulation engine
├── evaluation/             # Evaluation framework  
├── modes/                  # Agent implementations
├── llm/                    # LLM integrations
├── config/                 # Configuration management
├── data_generation/        # Data generation tools
└── examples/               # Usage examples
```

## 📊 Task Categories

OmniEmbodied supports diverse task types:

**Single-Agent Tasks:**
- **Direct Command** - Simple instruction following
- **Attribute Reasoning** - Reasoning about object properties
- **Tool Use** - Manipulating tools and objects
- **Spatial Reasoning** - Understanding spatial relationships
- **Compound Reasoning** - Multi-step problem solving

**Multi-Agent Tasks:**
- **Explicit Collaboration** - Direct agent coordination
- **Implicit Collaboration** - Indirect coordination through environment
- **Compound Collaboration** - Complex multi-agent scenarios

## 🔧 Configuration

OmniEmbodied uses flexible YAML configuration:

```yaml
# Basic configuration
agent_config:
  agent_class: "modes.single_agent.llm_agent.LLMAgent"
  max_history: 20

llm_config:
  model_name: "gpt-4"
  temperature: 0.1

evaluation:
  task_type: 'independent'
  default_scenario: '00001'
```

See the [Configuration Guide](https://omniembodied.readthedocs.io/en/latest/configuration.html) for details.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](https://omniembodied.readthedocs.io/en/latest/developer/contributing.html) for details.

### Development Setup

```bash
git clone https://github.com/omniembodied/OmniEmbodied.git
cd OmniEmbodied
pip install -e .
pip install -e OmniSimulator/
pip install -r requirements-dev.txt
```

### Running Tests

```bash
pytest tests/
```

## 📖 Citation

If you use OmniEmbodied in your research, please cite:

```bibtex
@software{omniembodied2024,
  title={OmniEmbodied: A Comprehensive Simulation Platform for Embodied AI},
  author={OmniEmbodied Team},
  year={2024},
  url={https://github.com/omniembodied/OmniEmbodied}
}
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [https://omniembodied.readthedocs.io/](https://omniembodied.readthedocs.io/)
- **Issues**: [GitHub Issues](https://github.com/omniembodied/OmniEmbodied/issues)
- **Discussions**: [GitHub Discussions](https://github.com/omniembodied/OmniEmbodied/discussions)
- **FAQ**: [Frequently Asked Questions](https://omniembodied.readthedocs.io/en/latest/faq.html)

## 🌟 Acknowledgments

OmniEmbodied builds upon research in embodied AI, multi-agent systems, and task-oriented dialogue. We thank the research community for their foundational contributions to these fields.
