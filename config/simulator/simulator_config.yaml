# Simulator global configuration
explore_mode: thorough   # normal/thorough
global_observation: false  # Whether to enable global observation mode (all items are initially discovered)

# Task verification configuration
task_verification:
  enabled: true          # Whether to enable task verification functionality
  mode: "step_by_step"   # Verification mode: step_by_step/global/disabled
                         # step_by_step: Perform verification at each step and return subtask completion status
                         # global: Only perform global verification when done command is entered
                         # disabled: Do not perform any task verification
  return_subtask_status: true      # Whether to return subtask status at each step (only effective in step_by_step mode)

# 可视化配置
visualization:
  enabled: false          # 是否启用可视化系统
  web_server:
    host: "localhost"     # Web服务器主机地址
    port: 8080           # Web服务器端口
    auto_open_browser: true  # 是否自动打开浏览器
  request_interval: 2000  # 前端HTTP请求间隔 (毫秒) - 控制界面更新频率
 
