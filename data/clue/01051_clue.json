{"id": 1051, "raw": "\nRetrieved from http://www.ecmag.com/section/systems/mainframe-digital?qt-issues_block=1\nText:\nData centers first appeared in the 1970s as mainframe computing environments. Their design is still evolving to best operate efficiently and reliably. The vast amount of information flowing in and out of a data center requires that all phases of design be viewed as a way to help ensure that the data center itself will function as needed. The design can come after the exterior has been completed, though tackling challenges earlier is advantageous to contractors and integrators.\n\nWhile data centers deal with a multitude of specialized systems and components, they are traditional systems that need to be hearty enough to support everything else contained within.\n\n“When you go to build a data center, you must do the design first and you need to start with the cooling,” said <PERSON>, founder of Premier Solutions.\n\n<PERSON>, founder of PTS Data Center Solutions agreed, describing key criteria with the following:\n\n  1. What is the total load being designed for? This must be defined for both the day one as well as the future maximum load.\n  2. What is the availability hoped to be achieved for the site? This is often defined in terms of the Uptime Institute’s Data Center Rating Guideline but not always.\n  3. What is the density at which the critical load will be deployed?\n\n“Because load densities are widely varying in a typical data center, we prefer to discuss density in terms of watts per rack/cabinet as opposed to watts per square foot of raised floor,” <PERSON><PERSON> said.\n\nKeeping cool\n\nGeneral power consumption is an inherent data center concern. The massive amount of equipment both pulling power and generating heat causes issues of cooling to reign as the dominant headache facing data center designers. This becomes apparent early in the design process.\n\n“You get in to how many servers will reside in a cabinet, how many cabinets will be in a room, is the UPS going to be large enough to handle what is being put in. Then you start having to deal with the issues of loads and cooling,” Montgomery said.\n\n“There are two major pieces to data center cooling system design,” Sacco said. “The first is the heat-removal piece. This is sometimes referred to as the air side of the cooling system. This piece refers to the CRAC or CRAH units deployed inside the computer room that are responsible for removing the heat generated by the IT [equipment] load. The second is the heat rejection piece. This piece is responsible for dissipating the heat into the ambient environment. There are many options to accomplishing this, but the most common are direct expansion [DX], or compressed refrigerant approaches, such as air-cooled condensers, glycol-based dry coolers, and water-based cooling towers, or via a myriad of chilled water approaches. Each approach has its own unique CAPEX and OPEX considerations which must be aligned with the projects requirements, budget and schedule.”\n\nOptions also exist in terms of how to best handle the heat generated within a data center, though no single option is the perfect fit for every data center. Every data center is unique, and each design will have different variables and components.\n\n“There are a few ways to cool a data center. The traditional way is room based where cooling sits along the perimeter of the room. Then you have row based cooling that chills by row, which is closer to the heat source. Those are the two most common types these days,” Montgomery said. “You first need to design the room or data center and determine if it will be room- or row-based cooling. After that you can design the rest of the infrastructure around that. There is a higher upfront cost associated with row-based, however, the [return on investment] over time supports that cost. In a data center, you have huge losses of cooling in a room-based cooling setup, huge as in about 60 percent loss by the time the cooling hits the servers, which is where the heat is being generated.”\n\n“PTS commonly designs and deploys data centers that use a common heat-rejection approach, but with multiple heat-removal methodologies to accommodate the load density deployed in a given region of the data center, such as using perimeter CRAC units for a general 5 kW per rack/cabinet cooling density, but the additionally deploying in-row, or close-coupled cooling solutions, or even in-rack cooling solutions to handle high and ultra-high density zones,” Sacco said.\n\nMontgomery said that many of the cooling choices are dependent upon the servers being used in the data center.\n\n“The whole cooling choice and data center design really is a pyramid of qualifiers. You need to take in to account the types of servers, their location, the type of power available; it all comes in to play,” he said.\n\nOne way the actual placement of the servers in the racks has an impact on the room design depends on whether the servers are mounted top to bottom or bottom to top. That helps determine where the heat flow will go to primarily and thus dictates what areas need to remain cooled to help with optimal performance and operations. It is then imperative to take the number of servers being used in to consideration.\n\nThe use of high-density servers also compromises the data center overall in terms of heat generation. The heat put off by servers is dictated by the kW dissipated by each server, which is then compounded based on how many of those are in each rack.\n\n“Anything over 16kw is considered high density. What you generally see being designed is more of a medium density rack which runs at around 6–10 kW per rack,” Montgomery said.\n\nAfter the cooling has been designed, the power is the next system that is to be tackled.\n\n“You start having to make decisions and choices such as what UPS will be put in and whether or not you are going with a more traditional approach of N plus 2, which is using both a UPS and a generator to support the main feeds,” he said.\n\nSacco said the same critical design criteria considerations apply to the power systems design. In general, the two major pieces of data center power design are protection and distribution. For protection, decisions must be made on short-term protection (UPS) and long-term protection (emergency backup power). For distribution, appropriate distribution must be designed in accordance with the required loads. In all cases, the power systems configuration will vary greatly depending on the levels of capacity, redundancy, concurrent maintainability, and fault tolerance desired within the system.”\n\n“Each server cabinet is dual power, A and B, which means two power sources. The A feed goes to the UPS and the B feed then would go to a generator or other backup power source. The point of this is so that there is no single point of failure for any rack or any area in the data center,” Montgomery said.\n\nVarious design options exist, and the decision is based on location and what is readily available.\n\n“You could have two UPSs feeding each cabinet is you wanted or you could use utility or street power if available and reliable. But with utility power you get in to situations that come up such as power outages during hot months and spiking rates during certain months or even times of the day,” Montgomery said, adding that, in California, electric is billed using a four-tier system and the kw/hour charge can jump by as much as three times the normal rate during peak periods. “Because of those issues with utility supplied power, UPSs tend to win out during the data center design phase for most people,” he said.\n\nHowever, when using UPSs for both power and backup power, Montgomery said that designing their load usage and balancing is just as important.\n\n“You should never design to give one UPS any more than 50 percent usage because, if the other UPS fails and the first is past 50 percent usage, it cannot handle the extra load. You need to always make sure to calculate things properly and always re-run those calculations when new components are added or removed. Growth and runtime are big issues in a data center and you need to take those in to consideration and always be changing things as need be,” he said.\n\nThe cost of downtime\n\nMany in the industry cannot stress enough the importance of designing a data center for maximum redundancy to prevent downtime, which can translate to costly mistakes that the data center owner and operator have to absorb.\n\n“Take eBay. Through their data center they run $2,300 per minute in transactions. That can be costly if that data center goes down,” Montgomery said.\n\nBecause of the costs associated with both running and losing run time in a data center, contractors assisting in the design of data centers should always first determine what type of data center the customer has, either cost centers or profit centers. The determination helps then create the master plan for how the center will be designed in total.\n\nA cost-based data center, such as for Northrop Grumman and Boeing, supports the daily operations of the business as a whole, though revenue is not directly generated through the data center itself. They support the other business operations such as accounting, human resources, accounts payable, accounts receivable and other standard business processes. They impact business operations but not direct revenue streams.\n\nProfit-based data centers, such as for eBay, Amazon or Google, rely on the data center to generate revenue along with supporting business operations. Because these data centers are inherently important and essential to the health of the business itself, downtime is not an option. When a profit center goes down, revenue ceases. Therefore, these types of centers have a mandate that they need to be operational 100 percent of the time and no single point of failure can exist in the design.\n\nThe interdependence between systems is critical in a data center, especially when it comes to maintaining continual uptime and operations.\n\n“If the power goes out, then the cooling goes out,” Montgomery Said. “Then if the cooling goes out, the heat within the data center rises and it then becomes very hard to cool things down again even when the power comes back on. Because of all of the redundancy built in to the design, it is actually very rare for an entire data center to go down all at once.”\n\nContractors need to understand that data centers rely upon proper design and system installation. This, coupled with continual maintenance and monitoring of the data center as a whole, is crucial to their optimal performance and success. Comprehending how data centers have evolved and how today’s interconnected and integrated infrastructure can assist in making sure data centers operate properly is one of the keys to success.\n\n“Data centers have been around since the ’70s,” Montgomery said. “They started with mainframes and then moved to server-based during the late ’80s and early ’90s. Mainframe was the dominant computing model until ‘distributed’ processing began; where servers were spread out among the national companies sites, then consolidation took place to better manage servers. Digital/IP-based means of remote access further transformed data centers. Digital came about in IT around the mid to late ’90s and it changed our lives and the lives of IT. Thus, IT can now monitor and manage their operations and environments such as cooling, temperature, humidity, and power in real time and access the status anywhere. This digital IT component is very important in and after design, as many sites are dynamic and grow.”\n", "response": "### **Data Center Emergency Retrofit Scene**  \n\n#### **1. Overall Scene Theme and Core Concept**  \nA **mid-sized, high-availability data center** undergoing **emergency cooling system retrofitting** after a critical failure. The facility supports a **profit-based** operation (e.g., financial transactions, cloud services), meaning **downtime is catastrophic**.  \n\nThe scene is **dense with modular server racks, redundant power systems, and a mix of active and deactivated cooling infrastructure**. Teams of **technicians, engineers, and logistics personnel** must **collaborate under time pressure** to restore operations while avoiding cascading failures.  \n\n**Why Multi-Agent?**  \n- **Heavy machinery relocation** (CRAC units, backup generators) requires coordinated lifting.  \n- **Precision troubleshooting** demands simultaneous monitoring of power, cooling, and server health.  \n- **Safety-critical operations** (high-voltage power, hot server racks, pressurized coolant lines) necessitate role specialization.  \n\n---  \n\n### **2. Spatial Layout and Area Descriptions**  \nThe data center is divided into **five functional zones**, interconnected by a raised-floor corridor with cable ducts:  \n\n1. **Server Hall (Primary Computing Zone)**  \n   - **Purpose:** Core processing area with high-density server racks.  \n   - **Atmosphere:** Humming servers, flickering rack LEDs, residual heat from recent cooling failure.  \n   - **Key Features:** Hot/cold aisle containment, overhead cable trays, emergency exhaust vents.  \n\n2. **Cooling Mechanical Room**  \n   - **Purpose:** Houses **CRAC (Computer Room Air Conditioning) units** and **chilled water pumps**.  \n   - **Atmosphere:** Loud machinery, dripping condensation, ozone smell from electrical panels.  \n   - **Key Features:** Partially disassembled cooling pipes, diagnostic terminals, backup refrigerant tanks.  \n\n3. **UPS & Power Distribution Room**  \n   - **Purpose:** Uninterruptible power supply (UPS) and backup generator control.  \n   - **Atmosphere:** Low-frequency transformer hum, blinking status panels, faint burnt insulation smell.  \n   - **Key Features:** Dual-redundant power buses, switchgear cabinets, battery arrays.  \n\n4. **Network Operations Center (NOC)**  \n   - **Purpose:** Real-time monitoring of temperature, power, and server health.  \n   - **Atmosphere:** Tense, glowing dashboards, multiple live video feeds, scattered printed logs.  \n   - **Key Features:** Wall-mounted thermal maps, emergency shutdown controls, labeled keycard lockers.  \n\n5. **Storage & Logistics Closet**  \n   - **Purpose:** Houses spare parts, tools, and portable cooling units.  \n   - **Atmosphere:** Crowded, dimly lit, with a mix of new and salvaged equipment.  \n   - **Key Features:** Heavy-duty carts, labeled spare parts bins, partially unpacked replacement fans.  \n\n---  \n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **Server Hall**  \n**a. Anchor Furniture & Installations:**  \n- **Server Racks (x12)** – 42U height, dual-power feeds (A/B), locked front/back panels.  \n- **Hot Aisle Containment Doors** – Sealed with rubber gaskets, temperature warning stickers.  \n- **Overhead Cable Trays** – Bundled fiber and copper, some dangling where maintenance occurred.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Rack #7 (Critical Overheat)** – Blinking amber alarm LED, thermal label showing 45°C (red zone).  \n- **Portable Cooling Unit (Inoperative)** – Blocked intake vent, error code “E-04: Refrigerant Low.”  \n- **Emergency Shutdown Button** – Behind a break-glass panel, labeled “LAST RESORT ONLY.”  \n\n**c. Functional Ambient Objects:**  \n- **KVM Switch Console** – Logged into Rack #3, left idle on BIOS screen.  \n- **Cable Tester** – Left on a folding table, last used on a severed Cat6 cable.  \n- **Fire Suppression Nozzles** – Green “armed” status lights, but one is flickering erratically.  \n\n**d. Background & Decorative Objects:**  \n- **Faded “Data Integrity = Revenue” Poster** – Peeling at corners.  \n- **Dusty “Employee of the Month” Plaque** – Dated three years ago.  \n- **Half-empty Coffee Cup** – Cold, sitting on an outdated hardware manual.  \n\n---  \n\n#### **Cooling Mechanical Room**  \n**a. Anchor Furniture & Installations:**  \n- **CRAC Unit #3 (Disassembled)** – Open maintenance panel, detached coolant hoses.  \n- **Chilled Water Pump Array** – Pressure gauge showing 20 PSI (normal: 40-60 PSI).  \n- **Refrigerant Storage Rack** – Four cylinders (two empty, one labeled “R-134a”).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Leaking Pipe Junction** – Dripping glycol, pooling near electrical conduits.  \n- **Bypass Valve Control** – Currently set to “Manual Override,” red warning tag attached.  \n- **Thermal Camera** – Left on a toolbox, last used to scan pump motors.  \n\n**c. Functional Ambient Objects:**  \n- **Hydraulic Lift Cart** – Scuffed, rated for 500kg, parked near CRAC unit.  \n- **Digital Multimeter** – Probes still attached to a circuit breaker.  \n- **Spill Containment Kit** – Unopened, covered in dust.  \n\n**d. Background & Decorative Objects:**  \n- **Outdated Piping Diagram** – Taped to wall, with handwritten corrections.  \n- **Rusty “Safety First” Sign** – Hung crookedly near the door.  \n- **Discarded Work Gloves** – Stained with coolant, tossed in a corner.  \n\n---  \n\n### **4. Scene Affordances and Embedded Potential**  \n\n#### **Collaborative Transportation Affordances**  \n- **CRAC Unit Replacement (250kg, 1.8m tall)** – Requires **two agents** to maneuver safely around raised floor obstacles.  \n- **Server Rack Relocation (180kg, fully loaded)** – Must be **disconnected from power/cooling first**, necessitating coordination.  \n\n#### **Reasoning & Tool-Use Affordances**  \n- **Attribute-Based Reasoning:**  \n  - Among **five coolant tanks**, only **one** has:  \n    - **Blue cap** (others are black)  \n    - **Handwritten “HIGH PRESSURE”** label  \n    - **Gauge showing 800 PSI** (others are near-empty)  \n  - **Distractor:** A decorative **blue toolbox** nearby increases misidentification risk.  \n\n- **Compound Tool-Use Reasoning:**  \n  - **Problem:** A **locked server rack (Rack #9)** contains a failing drive.  \n  - **Solution:** The **keycard** is inside the NOC’s **labeled locker (#14)**, which itself requires a **4-digit code** (found on a sticky note under the keyboard).  \n\n---  \n\n### **Final Notes on Design Intent**  \nThis environment is **deliberately layered**:  \n- **High-stakes urgency** forces prioritization.  \n- **Multiple interdependent systems** (cooling, power, networking) require parallel workflows.  \n- **Realistic clutter** (outdated manuals, half-finished coffee) adds perceptual noise, demanding precise agent attention.  \n\nThe scene is **ripe for tasks** like:  \n- **Diagnosing and repairing** the cooling system leak before servers overheat.  \n- **Balancing power load** between UPS systems after a generator test.  \n- **Coordinating a hardware swap** under thermal constraints.  \n\nEvery object **exists for a reason**, whether as a **critical tool**, a **potential hazard**, or **atmospheric texture**."}