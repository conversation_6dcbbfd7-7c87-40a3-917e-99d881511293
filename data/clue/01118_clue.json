{"id": 1118, "raw": "\nRetrieved from http://msdn.microsoft.com/en-us/library/5e3s61wf(v=VS.90).aspx\nText:\nExport (0) Print\nExpand All\n\nHow to: Create Custom Performance Counters\n\nWhen you create a new counter, you first create a category, and then specify one or more counters to be included in it. You can do this in one of the following ways:\n\nThere are two special issues that you should consider when you create counters and categories. First, you cannot create custom categories and counters on remote computers. Second, your interaction with custom counters and categories is restricted to read-only mode unless you explicitly specify otherwise. In read-only mode, you cannot increment or decrement them or set the raw value or other values within them. You can use the ReadOnly property to set a custom counter to writable mode.\n\nIt is important to note the difference between creating a counter and creating an instance of the PerformanceCounter component. When you create a counter, you are creating a new category and its associated counters in the Windows operating system, rather than a component in your project or application. When you create an instance of the PerformanceCounter component, you create a component inside your Visual Studio project that references an external counter.\n\n\nThere are security restrictions that affect your ability to use performance counters. For more information, see Introduction to Monitoring Performance Thresholds.\n\nSecurity noteSecurity Note:\n\nWhen you create a performance counter, realize that the resource may already exist. Another process, perhaps a malicious one, may have already created the resource and have access to it. When you put data in the performance counter, the data is available to the other process.\n\n\nThe PerformanceCounter class is not fully supported on Microsoft Windows NT version 4.0. You can read from the system counters, but you cannot create, write to, or delete custom counters.\n\n\n\nTo create a new category and custom performance counter at design time\n\n  1. Open Server Explorer and expand the node for the server you want to view.\n\n\n    If the server you want is not listed, you have to add it. For more information, see How to: Access and Initialize Server Explorer/Database Explorer.\n\n  2. Right-click the Performance Counters node and select Create New Category.\n\n    The Performance Counter Builder dialog box appears.\n\n\n    To access performance counters, you have to be a member of the security group that has access to performance counters (for example, the Performance Monitor Users group). Additionally, you might receive prompts on Windows Vista when you try to perform an action that requires elevated privileges, even when you are running under administrative permissions. For more information, see Windows Vista and Visual Studio.\n\n  3. Enter a name and description for the category you want to create.\n\n\n    If you specify the name of an existing category, an error will be raised. To overwrite an existing counter category, you must first delete the category by using the Delete method, and then add a new category.\n\n  4. In the Counter List Builder frame, do the following:\n\n    1. Click the New button.\n\n    2. In the Counter frame, specify a name for the counter you want to create in the category.\n\n    3. Choose a type from the Type drop-down list.\n\n    4. Enter a description for the counter.\n\n  5. Repeat step 4 for each counter you want to create in this category.\n\n\n    Before you exit the dialog box, you can select any one of the counters in the Counters list and edit their values, or delete the counters.\n\n\n    By default, counters and categories you create in the dialog box are read-write enabled, but your interaction with them through an instance of the PerformanceCounter component will be restricted to read-only unless you specify otherwise.\n\nTo create a new category and set of performance counters programmatically\n\n  1. Create a collection of type CounterCreationDataCollection.\n\n  2. Create the counters you want to create as objects of type CounterCreationData and set their necessary properties.\n\n  3. Add the CounterCreationData objects to the collection by calling the collection's Add method.\n\n  4. Call the Create method on the PerformanceCounterCategory class and pass the collection to it.\n\n    The following example shows how to create a series of counters and pass them to the category when you create it:\n\n    \t\t// Create a collection of type CounterCreationDataCollection.\n    \t\tSystem.Diagnostics.CounterCreationDataCollection CounterDatas =\n    \t\t   new System.Diagnostics.CounterCreationDataCollection();\n    \t\t// Create the counters and set their properties.\n    \t\tSystem.Diagnostics.CounterCreationData cdCounter1 =\n    \t\t   new System.Diagnostics.CounterCreationData();\n    \t\tSystem.Diagnostics.CounterCreationData cdCounter2 =\n    \t\t   new System.Diagnostics.CounterCreationData();\n    \t\tcdCounter1.CounterName = \"Counter1\";\n    \t\tcdCounter1.CounterHelp = \"help string1\";\n    \t\tcdCounter1.CounterType = System.Diagnostics.PerformanceCounterType.NumberOfItems64;\n    \t\tcdCounter2.CounterName = \"Counter2\";\n    \t\tcdCounter2.CounterHelp = \"help string 2\";\n    \t\tcdCounter2.CounterType = System.Diagnostics.PerformanceCounterType.NumberOfItems64;\n    \t\t// Add both counters to the collection.\n    \t\t// Create the category and pass the collection to it.\n    \t\t\t\"Multi Counter Category\", \"Category help\",\n    \t\t\tPerformanceCounterCategoryType.SingleInstance, CounterDatas);\n\nCommunity Additions\n\n© 2014 Microsoft\n", "response": "# **Scene Concept: Server Monitoring Control Room**  \n\n### **Overall Scene Theme and Core Concept**  \nA high-tech **Server Monitoring Control Room** in a data center, where a team of technicians and engineers must collaboratively monitor, diagnose, and repair performance anomalies in real-time.  \n\nThe scene is inherently multi-agent due to:  \n- **Distributed tasks** (remote server access, physical hardware checks, network diagnostics)  \n- **Collaborative troubleshooting** (heavy server racks requiring multiple people, restricted security permissions)  \n- **Time-sensitive urgency** (performance counters dropping, overheating warnings)  \n\nThe room blends digital monitoring with physical infrastructure management, forcing agents to coordinate between virtual dashboards and manual interventions.  \n\n---\n\n## **Spatial Layout and Area Descriptions**  \n\n1. **Main Monitoring Hub** – Central workstation with multiple displays tracking live server metrics. Crowded with diagnostic tools, sticky notes, and half-empty coffee cups.  \n2. **Server Rack Alley** – A narrow corridor of towering server racks, some with blinking warning LEDs, others with open maintenance panels.  \n3. **Tool & Spare Parts Storage** – A cluttered corner with labeled bins of spare components, tangled cables, and diagnostic devices.  \n4. **Secure Access Terminal** – A locked-down secondary terminal with elevated permissions, requiring biometric verification.  \n5. **Break Area (Minimalist)** – A small table with a microwave, a dying potted plant, and a stack of outdated server manuals.  \n\n---\n\n## **Detailed Area-by-Area Inventory**  \n\n### **1. Main Monitoring Hub**  \n**a. Anchor Furniture & Installations**  \n- **6-monitor workstation** (3 curved ultrawide, 3 vertical for logs, mounted on an adjustable arm)  \n- **Ergonomic chair with torn mesh backing** (wheel locked in place from years of pressure)  \n- **Overhead cable management tray** (filled with tangled Ethernet and power cables)  \n\n**b. Key Interactive & Task-Relevant Objects**  \n- **Custom performance counter dashboard** (one display shows a critical \"CPU_THROTTLE_WARNING\" in red)  \n- **Physical reset button panel** (labeled with masking tape: \"DO NOT TOUCH WITHOUT AUTH\")  \n- **Authentication keycard reader** (flashing red—requires admin override)  \n- **Emergency kill-switch handle** (behind a plastic cover, needs two people to pull simultaneously)  \n\n**c. Functional Ambient Objects**  \n- **Label maker** (out of tape, sitting atop a stack of misprinted labels)  \n- **USB hub** (one port loose, intermittently disconnecting peripherals)  \n- **Coffee-stained keyboard** (sticky \"F5\" key from repeated refreshes)  \n- **Desk fan** (oscillating slowly, rattling due to a loose screw)  \n\n**d. Background & Decorative Objects**  \n- **\"Server Uptime: 99.99%\" motivational poster** (peeling at the corners)  \n- **Post-it notes** (one reads: \"Jenkins—DO NOT UPDATE COUNTERS TILL 3PM\")  \n- **Stale donut in a paper bag** (partially crushed under a manual titled *\"Legacy System Migration\"*)  \n\n---\n\n### **2. Server Rack Alley**  \n**a. Anchor Furniture & Installations**  \n- **Four 42U server racks** (each with a mix of blinking status LEDs, some racks partially open)  \n- **Overhead cooling ducts** (one vent dripping condensation onto a rack)  \n\n**b. Key Interactive & Task-Relevant Objects**  \n- **Faulty RAID array (Rack B3)** (status LED solid red, requires manual drive replacement)  \n- **Loose fiber-optic cable** (dangling from Rack A2, intermittently disrupting network traffic)  \n- **Thermal alarm** (beeping faintly near Rack D1—overheating detected)  \n\n**c. Functional Ambient Objects**  \n- **Maintenance cart** (holding a torque screwdriver, thermal paste, and anti-static wristbands)  \n- **Emergency power cutoff switch** (behind a plexiglass cover, needs two keys to activate)  \n\n**d. Background & Decorative Objects**  \n- **Dusty \"DO NOT BLOCK VENTS\" sign** (partially obscured by a misplaced server manual)  \n- **Faded Sharpie marks** (someone wrote \"DON’T TOUCH\" on a non-critical panel)  \n\n---\n\n### **3. Tool & Spare Parts Storage**  \n**a. Anchor Furniture & Installations**  \n- **Industrial shelving unit** (sagging slightly under the weight of spare components)  \n- **Locked cabinet** (labeled \"CRITICAL SPARES—AUTH REQUIRED\")  \n\n**b. Key Interactive & Task-Relevant Objects**  \n- **Replacement server blade (Model X-9000)** (last one in stock, buried under a pile of older models)  \n- **Diagnostic oscilloscope** (battery dead, needs external power)  \n- **Admin keycard (Level 3 clearance)** (hidden inside a hollowed-out *\"Networking for Dummies\"* book)  \n\n**c. Functional Ambient Objects**  \n- **Labeled bins of cables** (HDMI, USB-C, Cat6—some tangled beyond recognition)  \n- **Tape dispenser** (out of tape, roll sitting abandoned nearby)  \n\n**d. Background & Decorative Objects**  \n- **Dusty \"Employee of the Month\" plaque** (dated three years ago)  \n- **Half-assembled LEGO server rack** (abandoned on a shelf)  \n\n---\n\n## **Scene Affordances and Embedded Potential**  \n\n### **Collaborative Transportation Affordances**  \n- **Faulty server blade (Rack B3)** – Requires two people to safely extract (weight: 22kg, fragile connectors).  \n- **Emergency power cutoff** – Two-key system ensures no single person can shut down the system accidentally.  \n\n### **Reasoning & Tool-Use Affordances**  \n- **Attribute-Based Reasoning:**  \n  - Five identical-looking hard drives in storage, but only one matches the RAID array’s firmware version (labeled with a tiny silver sticker).  \n  - **Distractor:** A shattered SSD nearby has similar markings but is unusable.  \n\n- **Compound (Tool-Use) Reasoning:**  \n  - **Problem:** The thermal alarm is triggered by a blocked vent.  \n  - **Solution:** A compressed air canister is in the storage room, but the vent cover requires a Torx T8 screwdriver (currently in the maintenance cart).  \n\n---\n\n### **Final Atmosphere Notes**  \n- A constant low hum of servers, occasional beeps from alerts.  \n- Flickering fluorescent lights in one corner.  \n- The faint smell of ozone and burnt coffee.  \n\nThis dense, highly interactive environment is primed for complex collaborative tasks—balancing digital diagnostics, physical repairs, and security protocols."}