{"id": 1123, "raw": "\nRetrieved from http://stackoverflow.com/questions/11439776/xmpp-facebook-chat-with-non-friends\nText:\nTake the 2-minute tour ×\n\nWe are working on a app which allows user to login only through facebook. Now we are planning to bring in chat aspect using facebook xmpp.\n\nBefore i go ahead, juts wanted to check if it is possible for people to chat each other through our app even though they are not friends on the facebook?\n\nThis is important to us as our app might need interactions between users who are not friends on facebook.\n\nshare|improve this question\n\n2 Answers 2\n\nFacebook Chat on XMPP requires that you be Facebook friends with all chatting parties. Otherwise it could be used to spam people with messages from people they do not know.\n\nshare|improve this answer\nThanks for the answer. Dont you think people accessing the same application at a time might need to talk to each other? Say for example i have a location based app for car pooling,which uses facebook for user authentcation. Now i would like my users to chat each other and strike a better deal even when they are not friends on facebook. Is there anyway i can achieve this through facebook itself ? –  praveena_kd Jul 11 '12 at 19:26\nFacebook chat API is meant to allow you to recreate the Facebook chat experience as on facebook.com, hence it only allows messages between friends. Ref: developers.facebook.com/docs/chat Perhaps you could consider using a local Jabber service within your website to achieve this non-friends chatting functionality? –  <PERSON> Jul 11 '12 at 19:48\nyou are better off not using Facebook and go all out with XMPP because Facebook does not support XMPP. hence why you can't cross communicate. –  user595349 Mar 3 '13 at 9:14\n\nI'm not a Facebook developer, so I don't know if this helps. But keep in mind that you can send normal messages to non-friends as long as the recipient hasn't blocked them. In other words, you could try falling back to the regular messaging system if the recipient is not on the user's friend list; that is, simply route the \"chat\" through the normal messaging API instead. Similarly, if a non-friend sends you a message, then the app pulls that from the inbox and delivers it to your chat window.\n\nThere are two caveats to this approach, though:\n\n  1. Gaining access to the messaging API (at least for reading other users' messages) gives the app access to all of the user's inbox contents, and many users may be unwilling to grant that permission.\n  2. Facebook has said that the inbox isn't intended for real-time communication, and that users that \"misuse\" it may find their messaging feature temporarily disabled. See: http://www.facebook.com/help/132736263468691/\nshare|improve this answer\nThanks Danny. As you have rightly said the users may not be willing to share the permission for reading inbox contents. As of now for our app we have built the chatting mechanism using RabitMQ. –  praveena_kd Oct 15 '12 at 6:22\n\nYour Answer\n\n\n\n", "response": "### **1. Overall Scene Theme and Core Concept**  \n**Scene Type:** *A bustling co-working tech incubator focused on collaborative app development.*  \n**Unique Story/Challenge:** A diverse team of developers, designers, and product managers are working on a new social networking app with real-time chat features. The challenge is integrating multiple communication protocols (XMPP, RabbitMQ, and proprietary APIs) while ensuring seamless UX. The space is inherently collaborative due to shared tools, restricted access zones, and heavy equipment that requires team coordination.  \n\n### **2. Spatial Layout and Area Descriptions**  \nThe incubator consists of:  \n- **Main Development Hub** – Open-plan workspace with modular workstations, whiteboards, and testing rigs.  \n- **Server Room** – A restricted-access area housing networking hardware, humming with cooling fans.  \n- **Breakout Lounge** – Informal meeting space with couches, a snack station, and a wall-mounted TV showing live API analytics.  \n- **Prototyping Lab** – A workshop with 3D printers, soldering stations, and disassembled hardware.  \n- **Conference Room** – Glass-walled, with a smartboard displaying wireframes and a sticky-note-covered brainstorming wall.  \n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **A. Main Development Hub**  \n**a. Anchor Furniture & Installations:**  \n- A 4-meter-long **collaboration desk** with built-in cable management and monitor mounts.  \n- A **server rack mockup** (1.8m tall, wheels locked) simulating backend infrastructure.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Three developer laptops** (one with a cracked screen, one displaying a RabbitMQ debug log, one logged into Facebook’s dev portal).  \n- **A labeled \"API Bridge\" prototype** (20cm cube, blinking LEDs, requires two people to lift safely).  \n- **A locked admin terminal** (biometric scanner, inactive—requires a keycard from the server room).  \n\n**c. Functional Ambient Objects:**  \n- **A label printer** (out of tape, jammed).  \n- **A whiteboard** (partially erased, with “XMPP vs. WebSockets?” scribbled in red).  \n- **A coffee machine** (brewing, steam valve slightly leaking).  \n\n**d. Background & Decorative Objects:**  \n- **\"Hackathon 2023\" trophy** (dusty, missing its plaque).  \n- **A stack of GitHub commit logs** (printed, coffee-stained).  \n- **A dead potted cactus** (next to a USB desk fan).  \n\n---  \n\n#### **B. Server Room**  \n**a. Anchor Furniture & Installations:**  \n- **Primary server rack** (2m tall, 300kg, bolted to the floor).  \n- **Emergency power switch** (behind a plexiglass cover).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Facebook XMPP proxy server** (error light blinking, log screen showing \"AUTH FAILURE\").  \n- **A spare keycard** (in a drawer labeled \"Backup Access,\" under a pile of Ethernet cables).  \n\n**c. Functional Ambient Objects:**  \n- **Network diagnostic tool** (displaying packet loss stats).  \n- **A fire extinguisher** (mounted, last inspected 6 months ago).  \n\n**d. Background & Decorative Objects:**  \n- **A \"NO FOOD\" sign** (with a pizza sticker defacing it).  \n- **Dusty rack manuals** (for obsolete hardware).  \n\n---  \n\n### **4. Scene Affordances and Embedded Potential**  \n**Collaborative Transportation Affordances:**  \n- The **API Bridge prototype** (20kg, awkward grip points) requires two people to move without damaging its fragile connectors.  \n- The **server rack’s backup battery** (120kg, on wheels) must be rolled into place by a team to replace a failed unit.  \n\n**Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among five **USB drives** on the desk, only one has a **red casing, a \"CRITICAL\" label**, and **half its storage used**—but it’s buried under decorative **fake \"bitcoin\" novelty coins**.  \n- **Compound Tool-Use:** To fix the **leaking coffee machine**, agents must:  \n  1. Find the **maintenance manual** (in the lounge’s magazine rack).  \n  2. Retrieve the **sealant tape** (inside a toolbox in the prototyping lab).  \n\n**Ambient Noise as Challenge:**  \n- The **biometric scanner’s error beep** masks the **RabbitMQ server’s alert chime**, forcing agents to prioritize auditory cues.  \n- A **decoy keycard** (expired, left on the conference table) adds red herrings to access tasks.  \n\n---  \nThis environment is **dense with multi-agent triggers**: heavy objects, layered tool dependencies, and misleading clutter that demand precise communication and role delegation. Every object’s state (broken, locked, misplaced) creates a solvable problem—if the team collaborates."}