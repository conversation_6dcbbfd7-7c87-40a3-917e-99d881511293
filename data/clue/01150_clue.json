{"id": 1150, "raw": "\nRetrieved from http://www.gamefaqs.com/boards/916373-pc/68403068\nText:\n\n\nRecommend me a cheap case\n\n#1Lonestar2000Posted 1/23/2014 8:23:02 AM(edited)\nThe case for my media center is falling apart. I want a case that has the hard drives mounted with the connectors facing the side of the case and the PSU mounted on the bottom. It does not need to include fans since I have some already. Would prefer a front and side intake and a rear exhaust. $50 is my limit but I would prefer to spend less.\nRumble Roses. Someone enters the room.\n", "response": "### **Scene Concept: \"Repair & Retrofit – The Cluttered Computer Workshop\"**  \n\n#### **1. Overall Scene Theme and Core Concept**  \nA small, bustling computer repair workshop, specializing in custom builds and retrofitting old hardware. The space is an organized chaos of half-finished projects, salvaged components, and tools strewn about. The scene is inherently collaborative due to:  \n- **Heavy/Large Objects:** Server racks, packed shipping crates, and bulky old CRT monitors that require multiple people to move.  \n- **Specialized Tools:** Precision screwdrivers, thermal paste applicators, cable testers—each stored in specific locations, requiring coordinated retrieval.  \n- **Urgent Work Orders:** A whiteboard lists pending jobs, some marked \"RUSH,\" implying time-sensitive teamwork.  \n\n#### **2. Spatial Layout and Area Descriptions**  \nThe workshop is divided into four key zones:  \n\n1. **Main Workbench Area** – The heart of operations, covered in anti-static mats, with mounted magnifying lamps and a partially disassembled gaming PC.  \n2. **Storage & Salvage Corner** – Overflowing with mismatched PC cases, spare parts bins, and a tall metal shelving unit.  \n3. **Shipping & Receiving Station** – A cluttered desk with stacked boxes, a label printer, and a digital scale. One box is half-packed with bubble wrap spilling out.  \n4. **Testing & Burn-In Zone** – A secondary table with running test rigs, their fans humming, and a flickering old CRT displaying diagnostic logs.  \n\n#### **3. Detailed Area-by-Area Inventory**  \n\n##### **A. Main Workbench Area**  \n**a. Anchor Furniture & Installations:**  \n- A reinforced steel workbench (2m x 1m) with built-in power strips and cable management slots.  \n- A wall-mounted pegboard holding screwdriver sets, wire cutters, and zip ties.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Project Phoenix\" PC Case** – The current focus: an open ATX mid-tower (black, side panel removed) with a **misaligned PSU mount** (problem state) and **loose hard drive cage** (collaboration needed to stabilize while securing screws).  \n- **Thermal Paste Tube** – Half-used, cap missing, resting precariously near the edge.  \n- **Anti-Static Wrist Straps** – Two tangled together, hanging from a hook.  \n\n**c. Functional Ambient Objects:**  \n- A **USB microscope** (powered on, displaying a zoomed-in view of a motherboard trace).  \n- A **label maker** with a jammed tape cartridge.  \n- A **sturdy magnifying lamp** (swivel joint slightly loose).  \n\n**d. Background & Decorative Objects:**  \n- A **faded \"No Food or Drink\" poster** with coffee stains.  \n- A **dusty \"Best Overclocker 2012\" trophy** on a high shelf.  \n- A **stack of old PC Gamer magazines**, one open to a build guide.  \n\n---  \n\n##### **B. Storage & Salvage Corner**  \n**a. Anchor Furniture & Installations:**  \n- A **heavy-duty metal shelving unit (2m tall, 1.5m wide)** loaded with salvaged cases and components.  \n- A **large plastic bin** labeled \"MISC. CABLES – TEST BEFORE USE.\"  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Rumble Roses\" Retro Case** – A rare, bright pink early-2000s gaming case (side panel missing, **hard drives mounted sideways**, per the inspiration text).  \n- **PSU Testing Jig** – A modified power supply with alligator clips and a handwritten \"DO NOT TOUCH\" note.  \n\n**c. Functional Ambient Objects:**  \n- A **working but noisy dehumidifier** (keeps moisture-sensitive parts safe).  \n- A **labeled parts drawer cabinet** (one drawer slightly ajar).  \n\n**d. Background & Decorative Objects:**  \n- A **broken neon \"PC Repair\" sign** leaning against the wall.  \n- A **pile of empty energy drink cans** in a recycling bin.  \n\n---  \n\n##### **C. Shipping & Receiving Station**  \n**a. Anchor Furniture & Installations:**  \n- A **sturdy packing table** with a roll of bubble wrap mounted underneath.  \n- A **wall-mounted monitor** displaying the shop’s order queue.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Biolab Zeta\" Shipment** – A **sealed cardboard box (40x40x60cm, 18kg) with a red \"FRAGILE\" sticker** (collaboration required to lift safely).  \n- **Digital Shipping Scale** – Currently displaying \"ERROR: OVERLOAD\" (someone placed a heavy CRT on it).  \n\n**c. Functional Ambient Objects:**  \n- A **half-empty roll of packing tape** stuck to the table edge.  \n- A **stack of unlabeled USPS flat-rate boxes**.  \n\n**d. Background & Decorative Objects:**  \n- A **coffee mug filled with loose screws and bolts**.  \n- A **peeling \"Team Lift Zone\" sticker** on the floor.  \n\n---  \n\n##### **D. Testing & Burn-In Zone**  \n**a. Anchor Furniture & Installations:**  \n- A **sturdy folding table** with four test rigs running benchmark software.  \n- A **wall-mounted surge protector** with blinking status LEDs.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Diagnostic CRT\"** – An old **17-inch Sony Trinitron** displaying scrolling logs (flickering intermittently).  \n- **\"Overheat Risk\" Test PC** – A rig with a **faulty liquid cooler pump** (requires monitoring).  \n\n**c. Functional Ambient Objects:**  \n- A **USB-powered desk fan** (oscillating slowly).  \n- A **thermal camera** (left on, displaying a heatmap of a GPU).  \n\n**d. Background & Decorative Objects:**  \n- A **sticky note** reading \"Jenkins – fix BIOS settings!\"  \n- A **dusty VHS tape** labeled \"Backup – Do Not Erase.\"  \n\n---  \n\n#### **4. Scene Affordances and Embedded Potential**  \n\n**Collaborative Transportation Affordances:**  \n- The **\"Biolab Zeta\" shipment (18kg, fragile)** requires two agents—one to stabilize the base, another to guide it onto the table.  \n- The **retro \"Rumble Roses\" case** has an **unsecured side panel (hinge bent)**, needing one agent to hold it while another realigns it.  \n\n**Reasoning and Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among **five PSUs on the shelf**, only one fits the \"Phoenix\" build:  \n  - **Black Corsair RM750x** (correct) vs. **a silver EVGA (wrong wattage), a modular Seasonic (missing cables), and two non-modular no-names (one with a frayed wire).**  \n  - A **decorative blue glass paperweight** nearby adds noise—an agent must ignore it despite its similar color.  \n- **Compound (Tool-Use) Reasoning:**  \n  - **Problem:** The \"Phoenix\" PC has a **misaligned PSU mount**.  \n  - **Solution:** The **correct-sized rubber grommets** are stored in a **small parts drawer**—but a **stuck screwdriver** blocks it.  \n  - **Sub-task:** First, remove the screwdriver (which requires pliers from the pegboard).  \n\n---  \n\n**Final Notes:**  \nThis environment is **dense with intentional clutter**, allowing for:  \n- **Multi-agent coordination** (lifting, stabilizing, troubleshooting).  \n- **Tool-based problem-solving** (locating, retrieving, applying).  \n- **Real-world cognitive noise** (distracting but plausible objects).  \n\nThe scene is **ready for complex, emergent task generation**."}