{"id": 1182, "raw": "\nRetrieved from http://android.stackexchange.com/questions/948/how-can-i-watch-ted-videos-offline-on-android/1165\nText:\nTake the 2-minute tour ×\n\nI'm looking for a way of subscribing to and watching TED talk videos on the HTC Desire, but I want support for off-line viewing.\n\nThe Mother TED app seems to require an active network connection, which isn't suitable since I want to be able to watch videos on the underground.\n\nThere's an RSS feed of the mp4 videos here - is there anything like Google Listen that supports mp4 video?\n\nshare|improve this question\nGreat question. I'm kind of bummed that Listen doesn't support video podcasts yet. Otherwise if it did, that would solve the problem for sure. –  Webs Sep 4 '10 at 3:40\n\n5 Answers 5\n\nup vote 2 down vote accepted\n\nI use BeyondPod (the full version) to watch TED Talks all the time. It pulls in a list of the videos from the feed and you can choose to download which ever ones you want and then play them right in BeyondPod. There is no data connection required.\n\nshare|improve this answer\nExcellent, that does the trick. If I can get this question transferred over to my ownership I'll mark this accepted, in the mean-time, have a +1 :) –  therefromhere Apr 30 '11 at 22:04\n@therefromhere: I've pinged the dev team, since Mods can't do that. –  <PERSON>. May 2 '11 at 3:58\n@Al thanks. It's mine now. All mine! Muhahahaha. cough. –  therefromhere May 2 '11 at 9:11\n\nIt probably did not exist when you posted this question, but now TED Air can download a video to watch it offline.\n\nshare|improve this answer\n\nI would suggest using doubleTwist. doubleTwist will re-encode any video to be suitable for your device, simply by dragging the video onto your device listed in the program.\n\ndoubleTwist has built-in functionality for subscribing to podcasts, but I use iTunes to organize podcasts, then use doubleTwist to send the content to, and play the content on, my HTC Hero.\n\nshare|improve this answer\n\nAs of this date, simply download the official TED app and select the talk you like to view offline. Then tap the download icon to the top-right corner to download. You can then play the downloaded videos offline from My Talks tab.\n\nIf you are a kind of person who like to play the videos at higher speed, you can get apps that support fast playback such as VLC for Android or Dice Player. Since all the downloaded files are stored in /sdcard/Android/data/com.ted.android/files/, you can point the video player to that path.\n\nHope this helps someone.\n\nshare|improve this answer\n\nWow... I saw this quesion, thought \"there's gotta be something\"... but a quick look around the market and web search shows there's nothing that's popular. I did find this article about podcast managers for Android.\n\nI thought Google Listen could do this? Or maybe it only recently added that functionality?\n\nshare|improve this answer\nYou'd think so, but no, Google Listen doesn't support that yet. –  therefromhere Apr 30 '11 at 21:25\n\nYour Answer\n\n\n\n", "response": "### **Overall Scene Theme and Core Concept**  \n**Scene Type:** *Mobile Tech Support Hub in a Subway Station*  \n**Core Concept:** A bustling underground tech support kiosk designed to assist commuters with offline media solutions, inspired by the challenge of accessing TED Talks without an internet connection. The environment is inherently multi-agent due to its fast-paced, collaborative workflow—technicians must diagnose device issues, locate/download offline media, and provide quick fixes while managing limited physical space and shared resources.  \n\n### **Spatial Layout and Area Descriptions**  \nThe scene is a compact, modular tech hub tucked into a subway station corridor. It consists of:  \n1. **Main Service Counter** – A central workstation where technicians interact with commuters. Cluttered with diagnostic tools and demo devices.  \n2. **Media Download Station** – A bank of high-speed offline servers and flash drives for transferring TED Talks and other media.  \n3. **Repair Bench** – A cramped workspace with soldering irons, spare parts, and half-disassembled smartphones.  \n4. **Storage Nook** – Shelves packed with labeled cables, adapters, and backup devices.  \n5. **Waiting Area** – A small standing zone with charging ports and a digital display showing TED Talk previews.  \n\n### **Detailed Area-by-Area Inventory**  \n\n#### **1. Main Service Counter**  \n**a. Anchor Furniture & Installations:**  \n- A 2m-long laminated countertop with a scratched \"Tech Support\" sign.  \n- Two bolted-down swivel stools (one missing a wheel).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A demo tablet (cracked screen) looping TED Talk snippets.  \n- A \"Device Intake Form\" clipboard with illegible handwritten notes.  \n- A locked cash box ($5 visible through the slot).  \n\n**c. Functional Ambient Objects:**  \n- A label printer (out of tape, blinking \"ERROR\").  \n- A wireless barcode scanner (low battery indicator lit).  \n\n**d. Background & Decorative Objects:**  \n- A faded subway map with coffee stains.  \n- A sticky note reading \"DO NOT USE SERVER #3.\"  \n\n#### **2. Media Download Station**  \n**a. Anchor Furniture & Installations:**  \n- A server rack (1.8m tall) with four humming NAS drives.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A master terminal (login screen active, username \"admin_tech\").  \n- A tray of 32GB USB drives (one labeled \"TED Talks Backup\").  \n\n**c. Functional Ambient Objects:**  \n- A cooling fan (grinding noise).  \n- An Ethernet switch (two ports flashing amber).  \n\n**d. Background & Decorative Objects:**  \n- A crumpled \"Download Speed Chart\" taped to the side.  \n- A novelty \"I ❤️ Offline Mode\" mug (empty).  \n\n#### **3. Repair Bench**  \n**a. Anchor Furniture & Installations:**  \n- A steel workbench (stained with solder burns).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A disassembled HTC Desire (battery swollen).  \n- A \"Broken Devices\" bin (three phones with cracked screens).  \n\n**c. Functional Ambient Objects:**  \n- A magnifying lamp (flickering).  \n- A soldering iron (cool, but cord tangled).  \n\n**d. Background & Decorative Objects:**  \n- A half-eaten protein bar on a napkin.  \n- A Post-it note: \"Fix TED Air app crash bug.\"  \n\n#### **4. Storage Nook**  \n**a. Anchor Furniture & Installations:**  \n- Overstuffed wire bins (precariously stacked).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A bin of OTG cables (one with a frayed end).  \n- A box of \"Rare Adapters\" (missing HDMI-to-USB-C).  \n\n**c. Functional Ambient Objects:**  \n- A label maker (out of battery).  \n- A step stool (one leg slightly bent).  \n\n**d. Background & Decorative Objects:**  \n- A dusty \"Employee of the Month\" plaque (2018).  \n- A dead potted cactus.  \n\n#### **5. Waiting Area**  \n**a. Anchor Furniture & Installations:**  \n- A wall-mounted charging station (two ports broken).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A demo tablet running VLC (buffering symbol stuck).  \n\n**c. Functional Ambient Objects:**  \n- A QR code for \"TED Talk Subscriptions\" (scratched).  \n\n**d. Background & Decorative Objects:**  \n- A graffiti tag reading \"WIFI SUCKS.\"  \n- A crumpled TED Talk flyer (\"The Power of Offline Thinking\").  \n\n### **Scene Affordances and Embedded Potential**  \n**1. Collaborative Transportation Affordances:**  \n- The server rack (150kg) requires two agents to move (due to uneven floor tiles).  \n- The \"Broken Devices\" bin (overfilled, 25kg) needs teamwork to reorganize.  \n\n**2. Reasoning & Tool-Use Affordances:**  \n- **Attribute-based Reasoning:** Among five USB drives, only one has a \"TED Talks Backup\" label, but it’s partially obscured by a coffee ring. Agents must cross-check it against the server logs.  \n- **Compound Reasoning:** The locked cash box can only be opened by finding the key (hidden inside the \"Rare Adapters\" box, itself buried under loose cables).  \n\n**3. Dynamic Problem-Solving:**  \n- The flickering magnifying lamp needs a replacement bulb (stored in the Storage Nook, behind the dead cactus).  \n- The \"DO NOT USE SERVER #3\" note hints at corrupted files—agents must diagnose whether it’s safe to reboot.  \n\n**4. Ambient Noise as Challenge:**  \n- The sticky notes, illegible forms, and overlapping labels force agents to verify information actively (e.g., \"Is Server #3 really faulty, or is the note outdated?\").  \n\nThis environment is **ripe for emergent collaboration**: agents might need to simultaneously troubleshoot a device, fetch offline TED Talks, and manage impatient commuters—all while navigating clutter, broken tools, and conflicting clues."}