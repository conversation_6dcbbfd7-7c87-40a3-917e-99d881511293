{"id": 1191, "raw": "\nRetrieved from http://chowhound.chow.com/topics/820230\nText:\nHOME > Chowhound > Spirits >\n\n\nSpanish and Portuguese cherry liqueurs other than ginjinha (anise flavor present)?\n\n  • 2\n\nI recently had in Portugal ginjinha (ginja), the sour cherry flavored liqueur. At one of the ginja shops I also had a liqueur that had both cherry and anise flavors, but didn't get the name. Later in Madrid I was also given a couple of pours of a very similar cherry/anise flavored liqueur, described as typical of the region, but again did not get the name. Can anyone tell me what names they might be known as?\n\n\n  1. Click to Upload a photo (10 MB limit)\n  1. Twocents - Since your post has been around for a while, I'm hoping that you already discovered the name of your Madrid cherry/anise flavored \"pours\". They were probably creme de guindas (sour cherries). In my husband's home province, Asturias, they just call it anis de guinda. In the Basque country I think they call it pacharan. It was once typically home made by soaking the guindas in anisette for several months. I haven't found the correct sour cherry here, but have had great success substituting sour plums from \"ornamental\" trees.\n", "response": "# **Scene Concept: The Alchemical Distillery**  \n\n## **1. Overall Scene Theme and Core Concept**  \n\n**Theme:** A traditional Iberian distillery specializing in artisanal liqueurs, particularly cherry-anise blends, with a mix of historical charm and modern laboratory precision.  \n\n**Core Concept:** This distillery is a multi-room facility where handcrafted liqueurs are produced, bottled, and labeled. The space blends old-world techniques (copper alembics, handwritten recipes) with modern quality-control processes (sterile lab benches, digital scales). The inherently collaborative nature of distillation—requiring synchronized stages of preparation, fermentation, blending, and bottling—makes this ideal for multi-agent interaction. Agents must coordinate to move heavy casks, precisely measure ingredients, identify mislabeled bottles, and troubleshoot equipment failures.  \n\n**Why Multi-Agent?**  \n- **Heavy Equipment:** Large fermentation barrels, copper stills, and crates of bottled liqueurs require multiple hands to move.  \n- **Precision Tasks:** Some steps (e.g., measuring volatile anise extract) require one agent to stabilize while another pours.  \n- **Information Scattering:** Key details (recipes, labels, inventory logs) are spread across different rooms, necessitating communication.  \n\n---\n\n## **2. Spatial Layout and Area Descriptions**  \n\nThe distillery consists of four interconnected areas:  \n\n1. **Fermentation Cellar** – A cool, dimly lit stone-walled basement where cherry mash ferments in oak barrels. The air smells of yeast and faint anise.  \n2. **Distillation Room** – The heart of the operation, dominated by gleaming copper stills and condenser coils. A chalkboard tracks batch numbers.  \n3. **Blending & Bottling Lab** – A sterile workspace with pH meters, labeled beakers, and a conveyor belt for filling bottles.  \n4. **Storage & Shipping** – A cluttered back room with crates, a labeling machine, and a corking station.  \n\n---\n\n## **3. Detailed Area-by-Area Inventory**  \n\n### **A. Fermentation Cellar**  \n\n**a. Anchor Furniture & Installations:**  \n- Three **oak fermentation barrels** (1m tall, 80kg each when full, labeled \"Batch #22,\" \"Batch #23,\" and \"Batch #24\").  \n- A **hand-cranked pump** (rusted but functional) for transferring liquid between barrels.  \n- A **stone sink** with a slow drip leak.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A **hydrometer** (floating in a testing jar, reading 1.045 SG—indicating incomplete fermentation).  \n- A **leaking barrel** (Batch #23, seeping cherry mash onto the floor).  \n- A **locked cabinet** (containing pH stabilizers, key hidden under a loose floorboard).  \n\n**c. Functional Ambient Objects:**  \n- A **wooden mash paddle** (leaning against the wall, sticky with residue).  \n- A **clipboard** (with fermentation logs, last updated 3 days ago).  \n- A **wheeled dolly** (useful but currently blocked by a fallen barrel).  \n\n**d. Background & Decorative Objects:**  \n- Cobwebs in the corners.  \n- A **faded poster** (\"Fermentation Timetable, 1927 Edition\").  \n- A **broken glass jug** (shards near the sink).  \n\n---\n\n### **B. Distillation Room**  \n\n**a. Anchor Furniture & Installations:**  \n- A **large copper alembic still** (2m tall, 150kg, requiring two agents to tilt for cleaning).  \n- A **condenser coil** (dripping faintly, needing adjustment).  \n- A **steel workbench** (scratched, stained with liqueur spills).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A **stuck valve** (on the still, requiring a wrench to open—tool missing).  \n- A **handwritten recipe** (\"Guinda-Anis Blend, 40% ABV\") pinned under a magnet.  \n- A **malfunctioning thermometer** (stuck at 90°C, actual temp unknown).  \n\n**c. Functional Ambient Objects:**  \n- A **set of copper funnels** (varying sizes).  \n- A **steel bucket** (half-full of discarded fruit pulp).  \n- An **alcohol meter** (calibration slip says \"Last checked: May 12\").  \n\n**d. Background & Decorative Objects:**  \n- A **dusty award plaque** (\"Best Artisanal Liqueur, 1985\").  \n- A **chalkboard** with half-erased calculations.  \n- A **shattered Erlenmeyer flask** (long abandoned in a corner).  \n\n---\n\n### **C. Blending & Bottling Lab**  \n\n**a. Anchor Furniture & Installations:**  \n- A **stainless steel blending vat** (50L capacity, currently empty).  \n- A **conveyor belt** (jammed by a misaligned bottle).  \n- A **digital scale** (display flickering, needs recalibration).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A **mislabeled crate** (marked \"Pacharán\" but containing \"Anis de Guinda\").  \n- A **cracked beaker** (leaking cherry syrup onto the counter).  \n- A **locked recipe book** (combination written on a sticky note in the storage room).  \n\n**c. Functional Ambient Objects:**  \n- A **label printer** (out of red ink, only prints black).  \n- A **box of corks** (some deformed, needing sorting).  \n- A **pH testing strip roll** (nearly empty).  \n\n**d. Background & Decorative Objects:**  \n- A **dead potted plant** (on the windowsill).  \n- A **coffee-stained lab coat** (hung on a hook).  \n- A **stack of old invoices** (from \"Guinda Farms, 2019\").  \n\n---\n\n### **D. Storage & Shipping**  \n\n**a. Anchor Furniture & Installations:**  \n- A **pallet jack** (rusty but functional, max load 300kg).  \n- A **floor-to-ceiling shelf** (holding 200+ bottles in various stages of labeling).  \n- A **corking machine** (jammed by a misaligned bottle).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- A **leaking bottle** (label soaked, contents unknown).  \n- A **hidden crate** (marked \"Experimental Batch,\" sealed with wax).  \n- A **shipping manifest** (missing three entries).  \n\n**c. Functional Ambient Objects:**  \n- A **tape dispenser** (almost out).  \n- A **box of packing peanuts** (half-spilled).  \n- A **hand truck** (one wheel squeaky).  \n\n**d. Background & Decorative Objects:**  \n- A **broken clock** (stuck at 3:15).  \n- A **faded map** (\"Cherry Orchards of Spain & Portugal\").  \n- A **dusty apron** (embroidered \"José, 1972\").  \n\n---\n\n## **4. Scene Affordances and Embedded Potential**  \n\n### **Collaborative Transportation Affordances**  \n- **Copper Alembic Still (150kg, 2m tall)** – Requires two agents to safely tilt for cleaning or repair.  \n- **Fermentation Barrel (80kg, leaking)** – One agent must stabilize while another patches the leak.  \n\n### **Reasoning and Tool-Use Affordances**  \n- **Attribute-Based Reasoning:** Among five cherry liqueur bottles, one is mislabeled (correct one has a **blue wax seal, handwritten \"40% ABV\"**, while others are stamped). The **decorative blue glass decanter** nearby adds visual noise.  \n- **Compound Tool-Use:** To fix the **stuck valve**, agents must:  \n  1. Find the **missing wrench** (buried under paperwork in the lab).  \n  2. Use it while another agent **holds the still steady**.  \n\n### **Dynamic Problem States**  \n- **Jammed conveyor belt** (requires removing a **misaligned bottle** while avoiding breakage).  \n- **Broken thermometer** (agents must infer temperature via steam condensation).  \n\nThis environment is **dense, purposeful, and ripe for complex multi-agent tasks**—blending physical coordination, precise tool use, and contextual reasoning."}