{"id": 1216, "raw": "\nRetrieved from http://lists.w3.org/Archives/Public/www-style/2005Sep/0023.html\nText:\n\n\n\nFrom: <PERSON> <b<PERSON><PERSON><PERSON>@mit.edu>\nDate: Mon, 05 Sep 2005 19:01:21 -0500\nMessage-ID: <<EMAIL>>\nCC: <EMAIL>\n\n<PERSON> wrote:\n> In my recent testing on Windows browsers, I found them to be fairly well\n> supported\n\nWhile I can't speak as to why system colors were deprecated, I have to say that \nthe word Windows there is a key one.  The list of system colors in CSS2 was \nwritten based on the then-current Windows UI.  That means that they don't map \nwell to other user interfaces (eg various versions of MacOS, various Linux \ndesktops).  This I can say based on some implementation experience in Gecko\n\nFor example, let us consider two system colors quickly (quoting CSS2.1 section \n18.2 here):\n\n       Active window border.\n       Inactive window border.\n\nWhat happens when active/inactive state is not shown by changes in the border? \nShould the two be the same color?  How many web page designers will realize that \nthis can even happen if they're developing on Windows?  Similar problems plague \nother system colors -- the very definitions assume that certain classes of \nwindows exist, and that each has certain parts.  If windows outside this list of \nclasses exist, if parts outside this list exist, or if windows or parts of \nwindows in the CSS2 lists do NOT exist, things break down.\n\n > and would posit that they can have quite a valuable role to\n> play in creating accessible style sheets that match the user's set\n> colour scheme / preferences (e.g. if a user has set their Windows\n> environment to High Contrast, a web page can be styled to follow that\n> preference).\n\nBut if a user has their non-Windows environment set up for accessibility things \nwon't work because the colors that environment _does_ use are not reflected in \nthe Windows-centric system color list?  If this is the problem we're trying to \nsolve, system colors as present in CSS2.1 are not a good enough solution.\n\nReceived on Tuesday, 6 September 2005 00:01:40 GMT\n\n", "response": "# **Abandoned UI Design Research Lab**  \n\n## **1. Overall Scene Theme and Core Concept**  \nAn abandoned UI design research lab, frozen in time after a sudden evacuation. The space was once a cutting-edge facility for testing cross-platform interface accessibility, now left in disarray with half-finished experiments, scattered prototypes, and malfunctioning equipment. The lab’s unique challenge lies in its **hybrid physical-digital environment**, where agents must collaborate to recover lost data, repair broken systems, and decipher cryptic design documentation.  \n\n**Why Multi-Agent?**  \n- Heavy equipment requires coordinated lifting.  \n- Complex debugging tasks demand simultaneous input (e.g., one agent adjusts hardware while another monitors software feedback).  \n- Security systems mandate multiple authentication steps across different terminals.  \n\n---  \n\n## **2. Spatial Layout and Area Descriptions**  \nThe lab consists of four interconnected zones:  \n\n1. **Main Testing Chamber** – A cavernous room with modular wall-mounted displays, some cracked or flickering. The floor is cluttered with discarded ergonomic chairs and prototype input devices.  \n2. **Developer Workstations** – A bank of semi-enclosed cubicles, each containing a customized setup with mismatched peripherals. Several monitors display frozen error logs.  \n3. **Accessibility Simulation Room** – A dimly lit chamber with adjustable lighting rigs, tactile floor panels, and a wall of assistive devices (e.g., Braille terminals, eye-tracking rigs).  \n4. **Server Closet** – A cramped, overheated room housing legacy servers, their fans whirring unevenly. Exposed cables snake across the floor.  \n\n---  \n\n## **3. Detailed Area-by-Area Inventory**  \n\n### **A. Main Testing Chamber**  \n#### **a. Anchor Furniture & Installations**  \n- **Modular Display Wall** – A 6m-wide array of 24 mismatched monitors (CRTs, LCDs, touchscreens), mounted on a steel rail system. Half are powered off; others show distorted test patterns.  \n- **Prototype Testing Rig** – A hydraulic lift table (max load: 200kg) holding a disassembled \"universal input console\" (a Frankenstein device combining trackballs, foot pedals, and gesture sensors).  \n\n#### **b. Key Interactive & Task-Relevant Objects**  \n- **\"High Contrast Mode\" Calibration Device** – A standalone terminal with a stuck key (the \"H\" key is physically jammed down). Its screen shows an error: *\"Calibration incomplete: User preference conflict detected.\"*  \n- **Emergency Shutdown Panel** – A locked metal box (requires two keycards) containing a large red lever. Stenciled warning: *\"DO NOT PULL WITHOUT SYSTEM DIAGNOSTIC CONFIRMATION.\"*  \n\n#### **c. Functional Ambient Objects**  \n- **Adjustable Lighting Rig** – Four ceiling-mounted LED banks (two flickering at 30Hz). Control panel reads: *\"Simulate: Cataract / Glaucoma / Typical\"*.  \n- **Wheelchair-Optimized Desk** – Height-adjustable workstation with a touchscreen embedded in the surface (stuck in \"inverted colors\" mode).  \n\n#### **d. Background & Decorative Objects**  \n- **Faded UI Mockup Posters** – Torn concept art for *\"Project Hermes: One UI for All Platforms (2004)\"*.  \n- **Broken Coffee Maker** – Dripping stale coffee into a puddle of spilled sugar packets.  \n- **Abandoned Lunch Tray** – A petrified sandwich next to a sticky note: *\"Test Subject #14 – NO DAIRY\"*.  \n\n---  \n\n### **B. Developer Workstations**  \n#### **a. Anchor Furniture & Installations**  \n- **Ergonomic Testing Station** – A motorized chair with embedded pressure sensors (stuck in recline position). Mounted armrests hold a trackball and a chording keyboard.  \n- **Debugging Terminal Cluster** – Three vertically stacked CRT monitors displaying overlapping command prompts.  \n\n#### **b. Key Interactive & Task-Relevant Objects**  \n- **\"Legacy Color Sync\" Tool** – A floppy disk drive connected to a USB adapter. Label: *\"Win/Mac/Lin Gamma Values – DO NOT OVERWRITE\"*.  \n- **Locked Drawer** – Requires a 4-digit code (hint: written on a whiteboard in the next room). Inside: a prototype *\"contrast enhancer\"* glasses with cracked lenses.  \n\n#### **c. Functional Ambient Objects**  \n- **Voice Command Headset** – A microphone headset dangling from a hook. Tiny label: *\"Sample rate: 44.1kHz – TEST MODE\"*.  \n- **External Hard Drive Array** – Five drives daisy-chained via FireWire. LED indicators show three are powered but not spinning.  \n\n#### **d. Background & Decorative Objects**  \n- **Sticky Note Graveyard** – Hundreds of faded notes with cryptic reminders (*\"Fix palette #3 before demo!\"*, *\"Ask B. Zbarsky about Gecko bug\"*).  \n- **Dead Plant** – A withered succulent in a *\"World’s Best UI Designer\"* mug.  \n\n---  \n\n### **C. Accessibility Simulation Room**  \n*(Space constraints—see full document for remaining areas.)*  \n\n---  \n\n## **4. Scene Affordances and Embedded Potential**  \n\n### **Collaborative Transportation Affordances**  \n- **Hydraulic Lift Table (200kg)** – Requires two agents to operate the release lever while stabilizing the prototype console.  \n- **Server Rack Door** – A jammed 3m-tall cabinet door needs one agent to hold it open while another retrieves a backup drive.  \n\n### **Reasoning and Tool-Use Affordances**  \n- **Attribute-Based Reasoning:** Among five external hard drives, only one has:  \n  - A **red \"ARCHIVE\" sticker**  \n  - A **scratched-off serial number**  \n  - A **warm casing** (indicating recent activity)  \n  *(Distractor: Three non-functional drives with similar labels.)*  \n- **Compound Reasoning:**  \n  - **Problem:** The *\"High Contrast Mode\" terminal* is stuck due to a jammed key.  \n  - **Solution:** The *keycap puller* is in a drawer… but the drawer is locked with a code written on a whiteboard in another room.  \n\n---  \n\n**Final Note:** This environment is a playground for multi-agent problem-solving, where every object’s state, placement, and history feed into potential tasks—retrieval, repair, or forensic reconstruction. The density of *\"noise\"* (like the sticky notes or broken peripherals) forces agents to filter signal from chaos, just like debugging real-world systems."}