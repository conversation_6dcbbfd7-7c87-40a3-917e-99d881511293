{"id": 1222, "raw": "\nRetrieved from http://mathoverflow.net/questions/58256/which-topics-problems-could-you-show-to-a-bright-first-year-mathematics-student/58313\nText:\nTake the 2-minute tour ×\n\nI am teaching a one semester course (January to June) to first year students pursuing various different degrees. Because there are students studying actuarial science, physics, other sciences, other management or business sciences etc., the course has to be a generic one. By this I mean that we teach Calculus almost exclusively. Sure, there are topics like the Binomial Theorem and general remarks of proving theorems, but students that are interested in mathematics don't find the material particularly interesting. What is more, I can relate to them since I found the first two years of university mathematics somewhat boring. This included calculus, linear algebra, convergent/divergent sequences, multiple integrals etc. Real analysis (except for sequences), complex analysis, abstract algebra, topology and even elementary number theory do not appear until the third year.\n\nThe sad thing is that many students take mathematics only up to second year and do not get to see any of the \"cool\"/\"interesting\" mathematics even though they might be interested in mathematics. So I wondered: Is there any way of introducing \"interesting\" mathematics to them? (\"Them\" - in particular first years, but this question is also relevant for second years, who might have a higher degree of maturity.)\n\nSome things that I (and the other lecturers of this course) have thought of are:\n\nAdding challenging questions to tutorials (e.g. <PERSON><PERSON> or <PERSON>, though these are harder than we would like)\n\nWriting short introductory \"articles\" about fields or groups or perhaps the Euler characteristic (as an introduction to topology) etc. Of course, this is very idealistic, since one often doesn't have time or energy to do this.\n\nReferring them to library books where some of these things are explained. Also, quite idealistic, but how many will actually go to the library.\n\nThe best solution is probably to combine the three. Have a question which has a strange answer or solution, which can be explained by some interesting mathematics. Shortly explain how this is done, and have a reference where the student can go if he is interested enough to pursue it further.\n\nAre there any other ways of achieving this goal? Do you know of any questions to which this (combined) procedure can be applied?\n\nshare|improve this question\nSurely this question should be community wiki. –  Gerry Myerson Mar 12 '11 at 11:23\nA good problem for college freshmen who are not mathematics majors is Gale and Shapley's college admissions problem (master-ape.ens.fr/wdocument/master/cours/ecth17/galeshapley.pdf) which is also known as the stable marriage problem (en.wikipedia.org/wiki/Stable_marriage_problem). It's very relevant (the students were just admitted into college), it has wide appeal (especially its variations like the roommate problem, etc.), and its proof is very simple (no knowledge of calculus or even of algebra is required). You can discuss it during the first or second meeting. –  Joel Reyes Noche Mar 13 '11 at 12:57\n\n4 Answers 4\n\nI taught a class for advanced first year students at my university some time ago with the aim of showing them interesting aspects of university mathematics. It basically turned into a baby manifold course - with the goal of understanding the concept of de Rham cohomology. The point is that using just a little more machinery than the theory they already knew from calculus (e.g., div, curl, derviatives), it was possible to still prove some quite interesting results like the Brouwer fixed point theorem.\n\nI felt having this 'goal' in the course to be quite effective: the students always had some kind of idea where the course was going and most importantly, they knew why new concepts were introduced. There are a lot of motivating examples and questions to give them, like, 'How can one detect the shape of a space'? This made it easier to motivate concepts, like tangent spaces, differentials,etc.\n\nEven though your students would probably benifit from learning the standard material in calculus (you say they are not math majors), I think you should be able to incorporate some interesting examples like the above into your course. For example, when talking about Taylor series, you could give the nice proof of the irrationality of $e$ or $\\pi$. It would certainly make it more fun for you to teach, and probably not take too much of your time.\n\nFor reference, here are some of the topics we covered in our course:\n\nPolynomials, cubic equations, symmetric functions, Vieta's relations, special integrals and series, irrationality of $e$ and $\\pi$, number theory in finite fields, basic group theory, $\\mathbb{RP}^2$, stereographic projection, conics, Bertrand's postulate, divergence of $\\sum_{primes}\\frac1p$, generating functions,differentible manifolds, vector spaces, tangent spaces, differential forms, Stokes theorem, de Rham cohomology, Brouwer's fixed point theorem, Fundamental theorem of algebra, the fundamental group.\n\nThere are plenty of good books you could take a look at for finding interesting examples, e.g., I found the following books helpful.\n\nProofs from the Book by M. Aigner, G. Ziegler\n\nFrom calculus to cohomology by Ib Madsen, Jørgen Tornehave\n\nThe fundamental theorem of algebra by B. Fine, G. Rosenberger.\n\nshare|improve this answer\n\nIf the goal is to show them \"interesting\" mathematics, there are many sources available, depending on what you think falls into the class of \"interesting\" as well as appropriate. Just to start, Martin Gardner's books (and if you have a nice citation index, some books which cite Gardner's books) should keep you busy for a while. Using just his books alone, you can write a short description of 10 problems, each with three or four paragaphs of setup, result, and application, and refer to one of Gardner's books for an expansion on the topic. There are other authors you could use in place of Gardner.\n\nIf the goal is to stimulate their interest in mathematics through showing them problems, I humbly suggest the following: comb through open problem lists as well as indices of books like Gardner's, pick 100 or so problems from each, and start writing 10 lists of 20 problems, half which are solved/understood, half which aren't, and all of which are accessible. On MathOverflow alone, you see at least one such problem every two or three days. Then for the next ten semesters, you have a list of twenty problems from various fields to capture the students' imaginations. Bonus points to you if you can get them to research and cite the literature on what work has been done on the problems. Keep handy a record of your sources to compare to those found by your students.\n\nIf you are asking for specific problems from me, your question needs to say so, in which case I can tell you some problems I have worked on that are accessible that could do with some more elbow and brain grease applied to them.\n\nGerhard \"Ask Me About System Design\" Paseman, 2011.03.12\n\nshare|improve this answer\nThere are also the mechanics of presenting the list, finding incentives for students to work on something from a list, and so on. If you need suggestions for that as well, it should be made (to me, anyway) more clear in your post. Gerhard \"Afterthoughts: 12 for 10 cents\" Paseman, 2011.03.12 –  Gerhard Paseman Mar 13 '11 at 1:29\n\nI will write from the perspective of an undergraduate student. I think one reason undergraduate math is not interesting is the technical jargon laid before undergraduate students in the learning process. Usually one had to understand why one has to comes up with such a particular method, a concept, or a theory to understand the subject. The utility of any math they learned usually can only be manifested by sample calculations or problems.\n\nBut there is something much greater than this. For a really bright first year math student, I would assume normal course material is insufficient, and textbook+lecture+office hour do not solve his or her problems. Nevertheless the technical tool available to him or her does not enable him or her to do real research. I remember when I was a high school student I asked a math graduate student if there are two different holomorphic structures on a four dimensional manifold. At that time I know some complex analysis and some differential manifolds, but they are not sufficient to give an answer to this. Even now I do not know a yes or no answer in a form I can understand. A really bright first year math student is very likely to ask strange questions he or she cannot solve, and it stuck him or her so much that he or she decided to quit math. Equally likely is he or she found Putnam problems too technical, problem solving too time consuming, and in the end learning math becomes mastering a machine. I still remember the days I was stucking with my problems in reading Hatcher, at that time I was serious considering of transfering to history. And I am hardly a bright math student.\n\nThe solution to such a problem should be to guide him or her to read some master's work. If he or she can understand how a working mathematican did to tackle a problem, developing a math concept, or expanding a theory, it would be very beneficial because it helps the student to achieve a higher level of mathematical maturity unavailable by normal curriculum. Abel once replied \"I read the masters, not their pupils\". Clearly a lot of past mathematical work had been discarded or being forgotten, yet the professor can always choose something relatively more readable. If this is not possible, the professor should at least devise an individualized class with good supplementary reading.\n\nWhat I never understand is why the professor will adopt a useless undergraduate level textbook in his or her class instead of reading good math articles, or even lecture notes. I would hope the professor can pointed out where the material is insufficient, where the results had been updated, and where better strategies had been developed. A good list of sample problems coupled with good mathematical reading can change people. Sadly I did not had a chance to have such a class in US. As a result I had to go to Moscow, where I rediscovered math. But I hope future students would have a better fate.\n\nshare|improve this answer\n\nWe have been teaching an annual 8 lecture series mathematics course on topology of surfaces, Euler characteristic and loops on surfaces. It is more of discussion session than a lecture. Although it is primarily intended for a certain group of female undergraduates who are part of some program, I believe the topics could be introduced to any group of undergraduates who are interested enough! Here's the link for those interested :\n\n\nWe assume no basic background (of calculus or linear algebra) on their part. It is very hands on and they try to develop a concept of what a surface is and what numerical invariants help distinguish between them. Among other things, we encourage them to use play-doh to model filled-in surfaces and help them visualize deformations. We also have fun cutting Mobius strips along various curves and slice bagels into links! From my experience (and I have only taught it twice) some of these hands-on events always excite even the ones who aren't showing much interest. After all, it's fun for them to learn that the soccer ball has only so many pentagons and hexagons essentially due to Euler! They have to give a presentation at the end figuring out and explaining (from basic principles) how some problem (assigned to them) can be solved via topology - the solution could be in terms of pictures, play-doh or any other material if need be. For the interested, have a look at the presentations from last year :\n\n\nLastly, although we haven't done it the course referred to above, including something leading up to the four colour theorem should be plenty of fun too, both for the teachers and the students!\n\nshare|improve this answer\n\nYour Answer\n\n\n\n", "response": "### **Overall Scene Theme and Core Concept**  \n**Title:** *The Calculus Crisis – A Collaborative Math Lab Rescue*  \n\n**Core Concept:**  \nA university mathematics lab has been thrown into chaos after an experimental \"Topological Calculus Machine\" malfunctioned during a live demonstration for first-year students. The device, designed to visually demonstrate abstract mathematical concepts (like manifolds, Euler characteristics, and vector fields), has scattered its components across the lab, locked critical files, and disrupted essential tools.  \n\nThis scene is inherently collaborative because:  \n- **Heavy/Large Objects:** Some components (e.g., a dislodged projector, a tipped-over whiteboard) require multiple agents to reposition.  \n- **Multi-Step Reasoning:** Reconstructing the machine involves matching fragmented equations, identifying misplaced tools, and debugging corrupted digital displays.  \n- **Distributed Expertise:** Different agents may need to interpret mathematical notes, operate lab equipment, or physically reassemble components.  \n\n### **Spatial Layout and Area Descriptions**  \n1. **Main Demonstration Area** – The central space where the machine was being showcased. Now in disarray: scattered papers, a flickering projector, and a half-collapsed model of a Klein bottle.  \n2. **Storage Closet** – Contains spare parts, locked toolboxes, and a stack of old math competition problems (Putnam, IMC) used for reference.  \n3. **Professor’s Desk** – Strewn with coffee-stained notes, a locked laptop displaying a partial proof, and a disorganized bookshelf (Aigner’s *Proofs from THE BOOK* sticking out).  \n4. **Student Worktables** – Group study stations with half-solved calculus problems, open textbooks, and a spilled set of geometric solids (dodecahedron rolled under a chair).  \n5. **Equipment Corner** – A jumble of functional but misconfigured tools: a 3D printer mid-job (misaligned), a graphing calculator with a dead battery, and a tangled set of measuring tapes.  \n\n---\n\n### **Detailed Area-by-Area Inventory**  \n\n#### **1. Main Demonstration Area**  \n**a. Anchor Furniture & Installations:**  \n- **Topological Calculus Machine (TCM) Core Unit**: A 1.5m × 1m steel-framed apparatus with detached wiring harnesses and a cracked glass panel (revealing inner gears labeled \"∂/∂x\").  \n- **Collapsed Whiteboard**: 2m wide, partially detached from its wheeled stand (one wheel bent, requiring stabilization to move).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Projector**: Overheated, displaying a glitched vector field animation. Requires a reboot (power button jammed; needs two agents: one to hold the casing, another to pry the button).  \n- **\"Manifold Model Kit\"**: A scattered set of magnetic polygons (triangles, squares) with labels like \"χ=2\" or \"H₁=ℤ\". Three pieces are missing (check under desks).  \n\n**c. Functional Ambient Objects:**  \n- **Adjustable Lecture Stand**: Height stuck at 1.2m (wrench needed to loosen bolt).  \n- **Wireless Keyboard**: Missing the \"=\" key (likely stuck in the printer tray).  \n\n**d. Background & Decorative Objects:**  \n- **Faded \"P vs NP\" Poster**: Peeling at the corners.  \n- **Coffee Ring Stains**: On a stack of lecture notes titled \"Brouwer’s Fixed Point Theorem.\"  \n\n---  \n\n#### **2. Storage Closet**  \n**a. Anchor Furniture & Installations:**  \n- **Heavy Metal Shelving Unit**: 2m tall, slightly tilted (one loose bolt). Holds labeled bins: \"Spare Circuits,\" \"Graphing Tools.\"  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Locked Toolbox**: Requires a 4-digit code (hint: the digits are the first four primes scrambled). Inside: calibration screwdrivers for the TCM.  \n- **\"IMC 2011 Problems\" Binder**: Open to a page with a scribbled note: \"See Gale-Shapley for stability condition.\"  \n\n**c. Functional Ambient Objects:**  \n- **Spare Ink Cartridges**: For the printer, but one is leaking (needs cleanup).  \n- **Step Stool**: One rubber foot missing (wobbly).  \n\n**d. Background & Decorative Objects:**  \n- **Dusty Abacus**: On a high shelf.  \n- **Broken Slide Rule**: In a \"Misc.\" box.  \n\n---  \n\n#### **3. Professor’s Desk**  \n**a. Anchor Furniture & Installations:**  \n- **L-Shaped Desk**: Left side cluttered; right side has a pristine *Madsen & Tornehave* textbook (bookmarked to de Rham cohomology).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Locked Laptop**: Screensaver shows a partial equation: \"∇ × **F** = ❓\". Password hint: \"Favorite irrational number.\"  \n- **Drawer Key**: Taped under the desk (holds the TCM schematics).  \n\n**c. Functional Ambient Objects:**  \n- **Overflowing Pencil Cup**: Includes a protractor with a cracked hinge.  \n- **Stapler**: Jammed (requires force to open).  \n\n**d. Background & Decorative Objects:**  \n- **Framed Photo**: 1980s math Olympiad team.  \n- **Chalk Dust**: Coating a *Gardner’s \"Colossal Book of Mathematics\"*.  \n\n---  \n\n### **Scene Affordances and Embedded Potential**  \n\n**Collaborative Transportation Affordances:**  \n- **TCM Core Unit (150kg)**: Requires 3+ agents to lift safely (one to guide wiring).  \n- **Whiteboard (80kg)**: Needs two agents to stabilize while a third straightens the wheel.  \n\n**Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning**:  \n  - Among 5 chemical bottles in the closet, the one needed for the TCM coolant is the *only opaque blue bottle with a \"CORROSIVE\" label* (others are clear/red).  \n  - Distractor: A decorative blue vase on the professor’s desk.  \n- **Compound Reasoning**:  \n  - **Problem**: The TCM’s \"∂/∂x\" gear is misaligned.  \n  - **Solution**: Use the calibration screwdriver (locked in toolbox; code derived from prime numbers).  \n\n**Dynamic States & Hidden Dependencies:**  \n- The printer’s paper tray is stuck because the missing \"=\" key is lodged inside.  \n- The projector’s jammed button requires simultaneous pressure at two points (collaborative fine motor skill).  \n\n---  \n\n**Final Atmosphere Notes:**  \n- **Sounds**: Hum from the overheated projector, occasional *click* of the 3D printer failing.  \n- **Smells**: Burnt circuitry, old coffee.  \n- **Lights**: Flickering LED above the storage closet.  \n\nThis dense, layered environment ensures agents must:  \n1. **Communicate** to share clues (e.g., laptop password hints).  \n2. **Coordinate** to manipulate heavy/delicate objects.  \n3. **Reason** through mismatched parts and ambiguous labels.  \n4. **Adapt** to dynamic failures (e.g., printer jams mid-task).  \n\nThe scene is *overflowing* with potential tasks—from debugging equations to rescuing the Klein bottle model—all while navigating realistic clutter and distractions."}