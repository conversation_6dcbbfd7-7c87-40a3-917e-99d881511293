{"id": 1227, "raw": "\nRetrieved from http://meta.stackexchange.com/questions/155078/bounty-system-discriminates-against-new-users/155080\nText:\nWhat is meta? ×\n\nI like the bounty system -- I think it's a great motivator and an enjoyable, if intangible, method of showing/receiving appreciation.\n\nHowever, as a new user, I find it difficult (if not impossible) to participate.\n\nIn the first place, as a just-starting-out coder with little to offer by way of solutions, I nevertheless ask quite a few questions. There is an inverse relationship there that perhaps deserves further exploration and integration into how SO rewards work.\n\nFor new users it is easy and rewarding to ask questions, but I find myself handicapped and even penalized when it comes to expressing appreciation. For example, one cannot really reward two correct answers -- even if they each answer a different part of the question and the correct answer is comprised of both answers. As a solution, there is the bounty system -- but access begins at 75 rep and the lowest reward possible is 50 rep.\n\nAt the moment I have 86 rep and, morally, I owe two such bounties. I am holding off on paying the first until I can remain above 75 rep... but now I owe a second. Of course, strictly speaking I don't really owe anything; it is only my sense of decency to which I am beholden. However, these fellows (thecodeparadox in particular) really put some work into their answers and they deserve acknowledgement.\n\nNot yet having much to offer in the realm of providing answers, my rep is growing at a glacial pace. Is this fair to those answering my questions?\n\nWhat would you more experienced members say about providing some system for low-rep users to reward helpful responders? What would you think of something like this:\n\n  • a one-time bounty-purse (or access to a common bounty purse)\n  • not displayed to other users (as rep points, etc)\n  • the reward can be requested (rarely granted) or requested by a senior member\n  • awarded at mod discretion\n  • in order to compensate for the extra workload, mods receive a rep bonus for awarding such a purse, but a double-deduction if the purse must be rescinded\n  • awarded only to qualified low-rep users who display clear-thinking and erudition in their questions\n  • specifically to be used to reward 2nd answers and above-and-beyond answers\n  • subject to the oversight of the mod who awarded the bounty purse\n  • and able to be cancelled, with points granted rescinded, by the overseeing mod\n\nNecessarily, such a purse would be a somewhat rare award, but many of the badges on the SE network are difficult to attain.\n\nALTERNATELY, could there be created a common \"bounty-purse\" along with a method of petitioning a moderator to award a special bounty to a specific user, specifying the question under review along with the answer? The answerer would be notified that he is being reviewed for such a reward, which, in itself, is a reward -- all rewards on SO are intangible after all -- and perhaps this nomination can be added as a statistic...\n\nWhat think ye?\n\nshare|improve this question\nOnce you offer the bounty the rep is automatically removed from your account. So no worries on losing more rep when you assign the bounty to an answer. –  amanaP lanaC A nalP A naM A Nov 8 '12 at 18:50\nKeep in mind you can upvote all answers that you felt were helpful, not just the one that you actually used. It gives 2/3 the rep of an accepted answer. –  Servy Nov 8 '12 at 18:57\n@amanaP lanaC A nalP A naM A - I'm sorry - how did you come up with that comment? WHEN the rep is removed is entirely immaterial. If one only has 90 rep, then losing 50 rep is punitive. The time it takes to regain that 50 rep means that \"answerers\" (there must be a more correct term) are being penalized for responding to low-rep users. That is the issue, not when the rep is removed from one's account. –  gibberish Nov 8 '12 at 18:58\n@gibberish When answering questions the majority of rep gained comes from the community viewing the post and upvoting, not from the asker marking it as answered. It's one of the things I like best about SO as it doesn't provide all of the incentives to pander to the OP. –  Servy Nov 8 '12 at 19:00\nVery good point. Thank you. –  gibberish Nov 8 '12 at 19:01\nI see 4 downvotes to the question at this point. I can understand disagreement, but why the downvotes? What, specifically, makes this a stupid question rather than a matter of disagreement? –  gibberish Nov 8 '12 at 19:23\nAnd how, specifically, is Mr Palindrome's comment above (#1) both relevant to the question and worthy of upvote? –  gibberish Nov 8 '12 at 19:29\nOn Meta, downvotes are used to indicate disagreement. –  McCannot Nov 8 '12 at 19:29\nOh. I guess I misunderstood. There's a lot of that going around lately (referring again to self). Newbie-ness is an unpleasant state. –  gibberish Nov 8 '12 at 19:40\nThis is my new favorite phrase: \"it is only my sense of decency to which I am beholden\" –  jmfsg Nov 8 '12 at 20:00\n@gibberish From the MSO FAQ: \"voting is often used to express agreement or disagreement, not to point out a lack of quality or helpfulness.\". –  apsillers Nov 8 '12 at 20:07\nThen why subtract the rep points from a sincere question? That demotivates the asking of questions, does it not? One can expect the downvote if a question is frankly stupid, but if merely a matter of disagreement then a downvote connotes a strong element of discouragement. You can be sure that I won't be asking any questions on here again... –  gibberish Nov 8 '12 at 20:43\n@gibberish Rep on meta is generally seen not to really matter. –  JNK Nov 8 '12 at 20:51\nI suppose that makes sense... Although it's a bit like navigating one's way down a dark corridor, with shin-height obstacles, using a cardboard match for light. –  gibberish Nov 8 '12 at 21:10\n\n3 Answers 3\n\nup vote 7 down vote accepted\n\nFirst of all, please decouple using the bounty system to attract attention to an unanswered (or insufficiently answered) question from using it to reward the virtuous. The system was designed for the former, not the later. No one expects you to run around dropping post-hoc bounties to people who answered your questions. You'll never accumulate any rep that way.\n\nSecondly, I think that you are correct about the basic operation of the bounty system in it's intended and designed mode. It exists to allow people who have earned status on the site to incent other people. That design is, indeed biased, using the unbiased sense of the word 'biased'. It gives people with earned status more ability to attract attention and effort than people who have not.\n\nThe existence of this site, where, for free, you can post your problems and have a high expectation that others will solve them for you, should strike you as manna from heaven. To mix a metaphor, it's rather ungrateful of you to complain that some other people, by putting work into the site, can get even more goodness out of it.\n\nshare|improve this answer\nNot to detract from the other excellent responses to my question, but this is a fantastic answer. Clearly and concisely addresses the concerns behind my OP. The final paragraph should be framed and hanging on a wall. Thank you. –  gibberish Nov 8 '12 at 20:50\n\nupvote the answer and mark it as answered, 25 rep awarded. You can award additional bounty at your leisure if you say... answer some other people's questions.\n\nIf other people need answers to the question you've asked, the answer-er gets even more rep.\n\nyou may also upvote other helpful answers. 10 rep each. that 10-rep upvote is always appreciated.\n\nshare|improve this answer\nDo you mean, upvote the answer? Can one upvote one's own question? –  gibberish Nov 8 '12 at 19:48\n@gibberish yes i did mean upvote the answer. –  Sam I am Nov 8 '12 at 19:51\n@gibberish no, you can't vote your own question but you can upvote answer made on your own question. –  Shadow Wizard Nov 8 '12 at 20:24\n\nAs said from Sam I am, you can up-vote any answer given to your question, or other questions. The fact you accept an answer doesn't mean you cannot up-vote it, or up-vote any other answer that has been useful for you. You can up-vote answers given for questions asked from other users, if you find them useful. Up-voting doesn't cost you reputation.\n\nWith 30 votes per day, low reputation users can reward useful answers.\n\nBounties are the last resort to use when your question didn't get an answer, and you want to incentive users to answer it. Bounties could also be used as prize for great answers, but it must not be something done by default.\n\nshare|improve this answer\n\nYou must log in to answer this question.\n\nNot the answer you're looking for? Browse other questions tagged .\n", "response": "# **Scene Concept: \"The Stack Overflow Bounty Workshop\"**  \n\n## **1. Overall Scene Theme and Core Concept**  \nA bustling, semi-improvised tech workshop where novice and expert coders collaborate to solve complex coding challenges. The space is a hybrid between a hacker lab, a mentorship hub, and a resource exchange center—modeled after Stack Overflow’s reputation economy, but translated into a physical space where \"reputation points\" are literal tokens, and \"bounties\" are physical rewards locked behind collaborative problem-solving.  \n\n**Why Multi-Agent?**  \n- Heavy objects (server racks, toolboxes) require teamwork to move.  \n- Complex puzzles (debugging stations, locked bounty chests) require distributed knowledge.  \n- Resource scarcity (limited keys, shared tools) forces negotiation.  \n\n---  \n\n## **2. Spatial Layout and Area Descriptions**  \n### **A. The \"Newbie Corner\" (Entry Zone)**  \n- **Purpose:** Where inexperienced coders start, with basic tools but few privileges.  \n- **Atmosphere:** Cluttered, slightly chaotic, with half-finished projects and sticky-note reminders.  \n- **Key Features:**  \n  - A **Reputation Kiosk** (digital screen showing \"Rep: 86/100\").  \n  - A **Beginners’ Toolbox** (mostly empty, missing critical tools).  \n  - A **\"Question Board\"** (physical corkboard with handwritten notes).  \n\n### **B. The \"Bounty Workshop\" (Central Hub)**  \n- **Purpose:** The heart of the space, where bounties are posted and claimed.  \n- **Atmosphere:** Competitive yet cooperative, with teams huddling around workstations.  \n- **Key Features:**  \n  - A **Bounty Bulletin** (displaying tasks with reward values, e.g., \"Fix Memory Leak: 50 Rep\").  \n  - A **Locked Bounty Chest** (requires two keys held by different users).  \n  - A **Debugging Terminal** (showing an error log; requires simultaneous input from two users).  \n\n### **C. The \"Expert Lounge\" (Privileged Area)**  \n- **Purpose:** Reserved for high-reputation users, stocked with premium tools.  \n- **Atmosphere:** Organized but smug, with glass partitions separating it from the Newbie Corner.  \n- **Key Features:**  \n  - A **Reputation Vault** (stores spare \"rep tokens\" for bounties).  \n  - A **Moderator Terminal** (can approve/reject low-rep bounty requests).  \n  - A **\"Legacy Trophies\" Shelf** (dusty awards for past contributors).  \n\n---  \n\n## **3. Detailed Area-by-Area Inventory**  \n\n### **A. Newbie Corner**  \n**a. Anchor Furniture & Installations:**  \n- **Workbench (1.5m x 0.8m, scratched surface, wobbly leg).**  \n- **Reputation Kiosk (wall-mounted touchscreen, flickering slightly).**  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Beginners’ Toolbox (plastic, labeled \"BASIC TOOLS,\" missing screwdriver).**  \n- **\"Help Wanted\" Post-Its (pink, blue, and yellow, with scribbled questions).**  \n- **Broken Keyboard (missing \"Enter\" key, USB port bent).**  \n\n**c. Functional Ambient Objects:**  \n- **Coffee Maker (cold, empty carafe, \"Out of Order\" sticky note).**  \n- **Stack of Programming Books (dog-eared, one bookmarked on page 75).**  \n\n**d. Background & Decorative Objects:**  \n- **Dusty \"Welcome New Users!\" Banner (partially torn).**  \n- **Overflowing Trash Bin (crumpled energy drink cans).**  \n\n---  \n\n### **B. Bounty Workshop**  \n**a. Anchor Furniture & Installations:**  \n- **Central Worktable (steel, 2m x 1m, bolted to floor).**  \n- **Bounty Bulletin (magnetic board with color-coded task cards).**  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Debugging Terminal (dual-input required, shows \"Segmentation Fault\").**  \n- **Bounty Chest (50cm x 30cm, heavy-duty padlock, requires two keys).**  \n- **Tool Rack (missing the \"Precision Screwdriver,\" last seen in Expert Lounge).**  \n\n**c. Functional Ambient Objects:**  \n- **Whiteboard (half-erased, with a partial algorithm sketch).**  \n- **Printer (jam light blinking, out of paper).**  \n\n**d. Background & Decorative Objects:**  \n- **\"Reputation Rules\" Poster (faded, peeling at corners).**  \n- **Empty Pizza Box (stained with grease, dated yesterday).**  \n\n---  \n\n### **C. Expert Lounge**  \n**a. Anchor Furniture & Installations:**  \n- **Moderator Terminal (admin access, fingerprint scanner).**  \n- **Reputation Vault (heavy, 80kg, requires two people to lift).**  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Precision Screwdriver (last needed tool for Bounty Workshop task).**  \n- **\"Legacy Key\" (grants access to high-rep bounties).**  \n\n**c. Functional Ambient Objects:**  \n- **Ergonomic Chair (adjustable, slightly reclined).**  \n- **Mini-Fridge (stocked with energy drinks).**  \n\n**d. Background & Decorative Objects:**  \n- **Framed \"Top Contributor\" Certificates (dusty).**  \n- **Decorative Hourglass (sand stuck at the halfway mark).**  \n\n---  \n\n## **4. Scene Affordances and Embedded Potential**  \n\n### **Collaborative Transportation Affordances**  \n- **Reputation Vault (80kg, 1.2m tall):** Requires two agents to lift safely.  \n- **Debugging Terminal:** Needs simultaneous input from two users to bypass error.  \n\n### **Reasoning and Tool-Use Affordances**  \n- **Finding the Missing Screwdriver:**  \n  - Only one exists (expert lounge).  \n  - Must negotiate with high-rep users to retrieve it.  \n- **Unlocking the Bounty Chest:**  \n  - Requires two keys (one held by a newbie, one by an expert).  \n  - Forces cross-rank collaboration.  \n\n### **Compound Problem-Solving Example**  \n- **Problem:** Printer is jammed.  \n- **Solution:**  \n  1. Find the manual (buried under books in Newbie Corner).  \n  2. Retrieve the Precision Screwdriver (Expert Lounge).  \n  3. Clear the jam (two-handed operation).  \n\nThis design ensures a dense, interactive space where collaboration is **necessary**, not optional. The imbalance in resource access (newbies vs. experts) mirrors Stack Overflow’s reputation economy, while the physical constraints ensure teamwork is the only path to success."}