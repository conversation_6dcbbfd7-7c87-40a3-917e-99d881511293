{"id": 1228, "raw": "\nRetrieved from http://meta.stackexchange.com/questions/17685/etiquette-when-is-it-ok-to-edit-your-answer-to-include-information-from-someone/17693\nText:\nWhat is meta? ×\n\nI have been following this question with interest - History of public/private/protected - and noticed that shortly after a new answer was posted by <PERSON>, one of the original answers was updated to include this extra information. I have seen minor clarifications based on other posts before, but never such a wholesale import of another answer.\n\nWas this a reasonable thing to do here? Clearly, wanton cannibalism of other answers isn't reasonable on a site where multiple answers with distinct identities are supposed to compete. But there is an argument to seek to improve one's answer, particularly if a concept mentioned by another may be better phrased or explained.\n\nWhat are the boundaries that define when its acceptable to incorporate information from other answers into your own?\n\nEdit: <PERSON> points out that in this case, the datestamps prove the edit came before <PERSON>'s answer, so this is a terrible example. But the general question stands.\n\nshare|improve this question\nAnother example. Someone edited my answer into the accepted answer, and about a week back someone even downvoted me, probably because they thought I was plagiarizing the accepted answer, whereas it actually is the other way round. Here the events are months apart, rather than minutes ... –  takrl Sep 16 at 14:12\n@takri - Ouch. That's lame. Have an upvote. –  ire_and_curses Sep 16 at 18:27\n\n8 Answers 8\n\nup vote 7 down vote accepted\n\n\nIf you mean p<PERSON><PERSON><PERSON>'s answer, his edit pre-dates <PERSON>'s answer by 4 minutes... \"not guilty\", I say...\n\nFor some questions, it may also be useful / desirable to construct an aggregated answer that brings together the ideas from other answers. I haven't done this myself, but sometimes it works well. I would hope that they include attribution, though.\n\nEdit (this is a joke): I understand the concern of giving the most complete answer. But the point of such sites is to provide a \"collaborative answer\", through the voting system. ;-p\n\nshare|improve this answer\n@<PERSON> <PERSON>ll: Doh! You are absolutely right about the timings. I could have sworn they appeared in the opposite order... –  ire_and_curses Aug 26 '09 at 14:34\nMarc, you rep-whoring-stealer ! :P That was mine, mine, mine ! –  Gnoupi Aug 26 '09 at 14:45\n(though seriously, it took me 3 readings and \"finding it oddly familiar\" to understand the actual joke...) –  Gnoupi Aug 26 '09 at 15:04\n+1 for mentioning attribution. –  Andrew Grimm Jan 17 '11 at 2:47\n\nJoel has said on the podcast that he feels it is perfectly legitimate to find a question with a bunch of half-answers, and combine them into one answer, add a little more polish, and post that. He says it is a great way to earn reputation in a perfectly legitimate way.\n\nFrom Podcast 023:\n\nI did one of my \"just combine other people's answers\" into a well-written answer or a more exhaustive answer. And it was just like \"What's the difference between a statement and an expression?\" That was the question. And you know what, that's what I want. That article, I want that article. You know everybody's going to have questions like this. Newbies will always have the same questions.\n\nOr Podcast 004:\n\nWhaddaya do if you're reading some answers, and the best answer is there, and somewhere else is another answer that's also kinda good, and the combination of them would be great, and you wanna somehow combine these answers in a better answer. In other words, you just want to be an editor, like you're--a lotta the people who have contributed to our transcript pages for this podcast--and thank you all--a lot of them just went in and made a little correction. They just saw something that was obviously a typo, or some case where some previous person who'd been writing the transcript maybe didn't know how to spell someone's name, so they went in and they correct it. How do you do that when you're reading answers on StackOverflow, if you just want to make that little edit, to make that question better?\n\nshare|improve this answer\nAnd from Podcast 18 - Spolsky: ... I kinda suspect that maybe we want to kind of err in favor of more editing so that the, it's really a question of like how to make old questions get better and better and better and always be up to date. And part of making them better might even be taking you know three or four answers and combining them into one meta answer that really covers all the topics very elegantly. And I want to sort of see more of that than just people adding on their own tags saying you know Michael you got that wrong, it's actually 27, not 36. –  Robert Cartaino Aug 26 '09 at 14:41\nAnd which he does here, superuser.com/questions/1185/outlook-2007-wont-close/4332#4332, where he copies part of Diago's answer. I think he alluded somewhere that he wanted more of a wiki-like atmosphere. –  hyperslug Aug 26 '09 at 20:39\n\nI have done this only once. My answer had solved the original problem and been accepted, but the OP had asked another question in a comment on my answer. Another user had answered his comment in a new answer in a clean and concise manner, so I simply added his answer to mine (with attribution) and turned mine into a wiki.\n\nI personally see no problem with reusing another answer so long as attribution is given. In such a case, I think the post should be marked as wiki to signify that it is the collaborative work of multiple users. Seems I'm in disagreement with Spolsky on this particular point though.\n\nshare|improve this answer\n\n... distinct identities are supposed to compete.\n\nThe point of these sites is not to compete for reputation, it is to answer questions, and be highly relevant to search engine queries. As a result, the collaborative community is the \"distinct identity\" and the competition is with other sites like Experts Exchange.\n\nIt's even better if the highest upvoted answer is accepted, and improved upon either by the author, or other users of sufficient reputation.\n\nIf an answer is accepted but it wasn't the highest upvoted, I don't see any problem with incorporating other answers, because that improves the quality. If I Google search and I get a SOFU result, I want to read the accepted answer for all possible relevant bits. I don't want to go through and read the other postings to the question.\n\nshare|improve this answer\nThere is only one distinct identity...the community. That is the wiki-ness of the site. –  EBGreen Aug 26 '09 at 14:41\n\nI agree with what Jeff has said repeatedly on this matter. It is fine to combine answer parts into a complete answer. The goal is to answer the question for the OP who has probably been reading all along, but I think just as important (or more so even) is to answer it for future users. The ideal user experience should be:\n\n  1. User googles their question\n  2. S[OFU] is the #1 result\n  3. They go to the a well worded question that is written in a way that is clear matches their problem\n  4. They look and the top listed answer (or the top one or two if the OP accepted a different one) and it provides the complete and correct answer to their issue.\nshare|improve this answer\n\nI know I don't agree with Joel and Jeff on this one. I feel quite strongly that it is rep-whoring, and I think it is detestable. People who attribute the original answers I'd consider better, but only slightly.\n\nI understand why the site/community would want a rolled-up comprehensive answer. I agree it benefits everyone. So yes, I'd like to see such an answer - only if the person made it community wiki, and attributed the original answers\n\nI truly don't understand why it should be acceptable to have someone roll up all the good points from other answers and use it create a great answer, if they're doing it to bolster their reputation? To someone who comes along later, it'll appear that the person who created the rolled-up answer really knew their stuff. But they didn't, the community did. The reputation assigned should reflect that.\n\nThe people who posted the individual points contributed more than the person who just rolled them up together, and the reputation should reflect that too.\n\nI don't think anyone wants to see people who only create rolled-up answers and gain reputation as a result.\n\nshare|improve this answer\nI'm having trouble imagining someone who didn't know their stuff creating a good roll-up answer. I'd think you'd need at least a rough understanding of the problem domain to do more than smooth out grammar and formatting, and even then you'd have to know enough to choose compatible answers... But even if someone with no knowledge of the area in question showed up, took the top-voted answers and combined them while making them easier to read, i don't see that as a bad thing. If the combined answer isn't an improvement, then they'll have a hard time getting upvotes out of it... –  Shogging through the snow Aug 26 '09 at 17:29\n@Shog9 We are not on the same page. I'm more concerned about the good combined-up answers that contributed lots of rep to the poster, but was only a mash-up of existing answers. If they're doing this for community benefit, it should be community wiki. If they aren't, then they're unfairly reaping the benefits of others efforts. –  nagul Aug 26 '09 at 17:40\nWait, what's the point of posting a new, CW answer? Why wouldn't you just edit an existing answer to work in bits of another one, if you didn't want to see rep from it? Again, i'm talking about someone putting in some real effort here, not just copying two existing answers verbatim and concatenating them without regard to conflicts, grammar, formatting, or redundancy. –  Shogging through the snow Aug 26 '09 at 18:24\nYes, you could turn an existing answer CW. I am all for that. I'm talking about gaining rep points through this: \"Whaddaya do if you're reading some answers, and the best answer is there, and somewhere else is another answer that's also kinda good, and the combination of them would be great, and you wanna somehow combine these answers in a better answer. In other words, you just want to be an editor,..\". In simple terms, gaining rep points through edits (mash-up) of existing answers. –  nagul Aug 26 '09 at 18:33\nIf you're playing the role of editor, you have that choice every day: post a new answer, or edit to improve an existing one. This is merely a subset of that. If i edit your answer, you get the resulting votes; if i post my own, you don't. Generally, i make the call based on how much editing i'll have to do to make a good answer out of an existing one. –  Shogging through the snow Aug 26 '09 at 20:23\nIf a user combines good answers to a better one, that is an improvement of the sites's content. If a user improves the site's content, he gets rewarded by rep points. I see nothing wrong with this at all. –  Treb Aug 26 '09 at 20:50\n\nLegal boundaries:\nNone. Since posts are covered by CC licence, you are free to use content from other posts in yours.\n\nMoral boundaries:\nPhew, that's the difficult part. There are various statements, in the FAQ and on podcasts and blog entries, that explicitly say that this site is collaboratively edited. From that point of view, the behaviour you desribe is desired. However, in practice I see a much stronger sense of personal 'post ownership', both from the posters and others who refrain from copying parts of other answers. The appropriate way that is generally accepted by the community is to attribute the copied part to it's original poster, in form of a link to his answer or a comment to your answer.\n\nshare|improve this answer\n\nI have seen that happening some times indeed, and it looks a bit again like \"whoring\" for reputation.\n\nI mean, I understand the concern of giving the most complete answer. But the point of such sites is to provide a \"collaborative answer\", through the voting system. I mean that logically, the accepted answer actually solves the problem, but the most voted answers following are adding interesting details.\n\nThe system already works like this, so I don't see why one would want to aggregate the good answers from others, besides for taking all the credit (\"My answer is the most complete, watch !\").\n\nshare|improve this answer\nI think that it is desirable to have a 'most complete answer'. –  Treb Aug 26 '09 at 14:35\n@Gnoupi: because reading multiple, overlapping answers is tedious compared to reading one comprehensive one. Identity is a part of SO, but should take a backseat when it conflicts with quality! I refer you to the quotes in pkaeding's answer: meta.stackexchange.com/questions/17685/… –  Shogging through the snow Aug 26 '09 at 15:07\n\nYou must log in to answer this question.\n\nNot the answer you're looking for? Browse other questions tagged .\n", "response": "Here is a hyper-detailed collaborative scene inspired by the concept of information aggregation and attribution from the provided text:\n\n---\n\n### **Scene Theme: Digital Knowledge Hub - The Collaborative Editing Floor**  \nA bustling open-plan workspace where teams of researchers, editors, and fact-checkers collaborate to synthesize and refine knowledge from disparate sources. The chaotic yet purposeful environment is designed for multi-agent collaboration, with shared resources, overlapping workflows, and constant cross-referencing.  \n\n**Why Multi-Agent?**  \n- Heavy objects require team lifting (e.g., archive boxes, server racks).  \n- Tasks demand parallel workflows (e.g., verifying sources while drafting summaries).  \n- Embedded puzzles require tool-use reasoning (e.g., locked cabinets with keys hidden elsewhere).  \n\n---\n\n### **Spatial Layout**  \n1. **Central Editing Hub** – A ring of modular workstations with dual monitors, reference materials, and shared docking stations.  \n2. **Archival Storage Zone** – Floor-to-ceiling shelves holding labeled document boxes, vintage microfilm readers, and locked cabinets.  \n3. **Verification Lab** – Equipped with forensic tools (UV lights, magnifiers) and a wall-sized \"source reliability\" whiteboard.  \n4. **Breakout Nook** – A lounge area with coffee machines, snack wrappers, and a forgotten laptop charging on a side table.  \n5. **Server Closet** – Humming racks with blinking LEDs, backup tapes, and a tangled mess of Ethernet cables.  \n\n---\n\n### **Detailed Area Inventory**  \n\n#### **1. Central Editing Hub**  \n**a. Anchor Furniture & Installations**  \n- Six L-shaped workstations with ergonomic chairs (height-adjustable, wheels locked/unlocked).  \n- A 4m-long \"current projects\" magnetic whiteboard with color-coded sticky notes (some faded).  \n\n**b. Key Interactive Objects**  \n- *Drafting Table*: A half-finished composite report with paragraphs highlighted in yellow (needs attribution checks).  \n- *Reference Terminal*: A logged-in computer with 15 open browser tabs (StackExchange, JSTOR, Wikipedia).  \n- *Citation Printer*: Jammed with a crumpled page titled \"Podcast Transcript #023.\"  \n\n**c. Functional Ambient Objects**  \n- Three-ring binders (labeled \"Primary Sources A–M,\" spines cracked).  \n- A label maker out of tape, next to a spare cartridge (unopened).  \n- A wireless keyboard with sticky \"Ctrl+C\" and \"Ctrl+V\" keys.  \n\n**d. Background Objects**  \n- A \"NO PLAGIARISM\" poster with a coffee-cup ring stain.  \n- A dead ficus plant in the corner.  \n- A framed photo of a team-building retreat (2019, slightly crooked).  \n\n---\n\n#### **2. Archival Storage Zone**  \n**a. Anchor Furniture**  \n- Industrial shelving units (3m tall, shelves labeled \"1980–1990,\" \"Unverified Claims\").  \n- A locked fireproof cabinet (digital keypad, battery low warning light).  \n\n**b. Key Interactive Objects**  \n- *Box #42*: A sealed cardboard box (60x40x30cm, 22kg) labeled \"ORIGINAL ANSWERS – FRAGILE.\"  \n- *Microfilm Reader*: Powered on but displaying \"ERROR: Reel Misaligned.\"  \n\n**c. Functional Ambient Objects**  \n- A rolling ladder (one wheel squeaks).  \n- A cart with empty document trays (one tray labeled \"TO SHRED\").  \n\n**d. Background Objects**  \n- Dust bunnies under the shelves.  \n- A chalkboard with scribbled \"WHERE IS KEY??\" (partially erased).  \n\n---\n\n### **Embedded Affordances**  \n- **Collaborative Transport**: *Box #42* (22kg, bulky) requires two agents to lift safely.  \n- **Attribute-Based Reasoning**: Among 20 nearly identical USB drives in a drawer, only *one* has a red cap and a scratched label reading \"BACKUP_MASTER.\"  \n- **Compound Tool-Use**: The fireproof cabinet’s keypad is dead; its replacement battery is inside a locked desk drawer in the Editing Hub (key hidden under the ficus pot).  \n\n**Atmospheric Noise**: The hum of servers, the smell of toner, and a flickering fluorescent light in the Verification Lab create sensory distractions that demand precise agent focus.  \n\n--- \n\nThis scene is primed for tasks like:  \n- Synthesizing a report from fragmented sources (requiring division of labor).  \n- Recovering a corrupted file (needing tool retrieval across rooms).  \n- Resolving a \"citation conflict\" (cross-checking archival vs. digital records).  \n\nEvery object’s state and placement feeds into collaborative problem-solving!"}