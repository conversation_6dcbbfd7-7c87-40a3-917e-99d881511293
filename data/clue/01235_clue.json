{"id": 1235, "raw": "\nRetrieved from http://serverfault.com/questions/247327/multiple-public-ips-through-dd-wrt-without-1-to-1-nat\nText:\nTake the 2-minute tour ×\n\nI've done a search here and wasn't able to find anything relevant to my situation. I apologize in advance if I've missed an existing post on the topic.\n\nOur ISP has provided us with 6 static IP addresses. We are currently using two of them (plus one for the Comcast-provided router). One of the static addresses routes to our internal network, and the other goes to our VOIP phone system. Unfortunately, the Comcast machine doesn't support QoS, so our VOIP calls have been choppy.\n\nWe plan to put the Comcast-provided router into bridge mode and replace it with an ASUS RT-N16 running DD-WRT. However, I'm unsure how to set up DD-WRT to function similarly to our existing Comcast router. The Comcast router's WAN IP is the first of our static IP addresses. We did not need to provide an internal LAN IP address — simply connecting machines that use our other public addresses to the LAN ports on the Comcast router is enough for it to route between the connected machines and our internet connection.\n\nIs there a way to do a similar setup through the DD-WRT?\n\nThanks in advance.\n\nshare|improve this question\n\n2 Answers 2\n\nI am not sure how to do it on dd-wrt, but that is Linux-based so this should certainly be possible. See if you can go with a proxy arp. Basically you will be putting your /29 subnet on both the inside and outside adapters.\n\nshare|improve this answer\n\nDo you or do you not want private addressing? The simple solution here is to use a switch, instead of a router. Be sure each host is properly firewalled. I myself would rather use a proper hardware firewall to protect my network, have it proxy ARP the other addresses and setup static NATs to each host. DD-WRT may be able to do this, not sure.\n\nshare|improve this answer\nNo private addressing, if at all possible. We would like the machines with public addresses to actually have the public addresses. We need a router instead of a switch here, because we need to prioritize VOIP traffic from one of the public machines with QoS. –  Stephen Touset Mar 14 '11 at 20:11\nSeems to be on the DD-WRT wiki that setting QoS is pretty simple, but you're also after what setting to put this device in to basically bridge the outside to the inside. I believe it can do this and still meet your requirements, you'd have to test it. –  SpacemanSpiff Mar 14 '11 at 23:00\n\nYour Answer\n\n\n\n", "response": "### **Overall Scene Theme and Core Concept**  \n**Scene Type:** A high-tech **Network Operations Center (NOC) for a mid-sized ISP**, where engineers manage multiple static IP configurations, QoS prioritization for VOIP traffic, and network troubleshooting.  \n\n**Core Challenge:** A hardware failure in the primary router has forced a rapid migration to a backup DD-WRT-configured ASUS RT-N16, requiring multi-agent coordination to:  \n- Reconfigure multiple static IPs under time pressure.  \n- Troubleshoot VOIP call degradation due to improper QoS settings.  \n- Diagnose rogue ARP proxy behavior interfering with traffic.  \n\n**Why Multi-Agent?**  \n- **Physical Collaboration:** Heavy server racks, tangled cabling, and bulky networking equipment require two agents to move or adjust.  \n- **Information Asymmetry:** Different workstations display unique diagnostics (packet loss logs, ARP tables, QoS graphs).  \n- **Tool & Knowledge Specialization:** Some agents must operate crimping tools for cables, while others read router manuals or adjust hardware settings.  \n\n---  \n\n### **Spatial Layout and Area Descriptions**  \n1. **Main Router Station** – The heart of the NOC, dominated by a large rack housing the faulty Comcast router (in bridge mode) and the replacement ASUS RT-N16.  \n2. **VOIP Monitoring Desk** – A secondary workstation with real-time call analytics, a physical SIP phone, and a wall-mounted whiteboard listing priority IPs.  \n3. **Cable & Storage Closet** – A cramped side room with spare switches, loose Ethernet coils, and labeled bins of RJ45 connectors.  \n4. **Break Area (Distractor Zone)** – A small lounge with a coffee maker, scattered energy drink cans, and a bulletin board covered in outdated network diagrams.  \n\n---  \n\n### **Detailed Area-by-Area Inventory**  \n\n#### **1. Main Router Station**  \n**a. Anchor Furniture & Installations:**  \n- **19\" Server Rack (2m tall, 100kg)** – Mounted with:  \n  - **Faulty Comcast Business Router** (status LED blinking amber, \"Bridge Mode\" sticky note on side).  \n  - **ASUS RT-N16 (DD-WRT)** – Open case exposing internal PCB, USB serial debug cable attached.  \n- **Rolling Tool Cart** – Locking wheels (currently jammed), stacked with screwdrivers and anti-static mats.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **QoS Configuration Laptop** – Open to a DD-WRT admin panel, displaying incorrect bandwidth thresholds for VOIP.  \n- **Static IP List** – Printed spreadsheet taped to the rack, with three addresses crossed out in red pen.  \n- **Oscilloscope** – Probes dangling near the ASUS router, measuring signal interference.  \n\n**c. Functional Ambient Objects:**  \n- **Patch Panel** – 24 ports, half labeled with IP assignments (e.g., \"VOIP GW: ***********\").  \n- **UPS Battery Backup** – Audible alarm beeping intermittently (low battery).  \n- **KVM Switch** – Toggles between the QoS laptop and a secondary diagnostics PC.  \n\n**d. Background & Decorative Objects:**  \n- **\"Network Health\" CRT Monitor** – Showing a long-irrelevant SNMP graph from 2018.  \n- **Coffee-Stained Router Manual** – Dog-eared page on \"Proxy ARP Settings.\"  \n- **Dusty Cable Tester** – Buried under a pile of loose Zip ties.  \n\n---  \n\n#### **2. VOIP Monitoring Desk**  \n**a. Anchor Furniture & Installations:**  \n- **L-Shaped Workstation** – Two monitors: one running Wireshark, the other displaying jitter metrics (currently spiking to 120ms).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **SIP Phone (Yealink T48S)** – Call active, audio crackling. Physical \"QoS PRIORITY\" sticker on handset.  \n- **IP-PBX Server** – Open terminal showing `arp -a` output with duplicate entries.  \n- **VOIP Test Script** – Printed flowchart with handwritten notes: \"Check DSCP tagging!!\"  \n\n**c. Functional Ambient Objects:**  \n- **Headsets (x2)** – One with a frayed cable.  \n- **Label Maker** – Out of tape, \"LOW INK\" LED lit.  \n\n**d. Background & Decorative Objects:**  \n- **Framed \"Employee of the Month\" Certificate** – Dated 6 years prior.  \n- **Dead Ficus Plant** – One plastic leaf dangling over a keyboard.  \n\n---  \n\n### **Scene Affordances and Embedded Potential**  \n\n**Collaborative Transportation Affordances:**  \n- **Server Rack (100kg, 2m tall)** – Requires two agents to safely tilt for rear cable access.  \n- **Spool of Cat6 Cable (30kg, 1.5m diameter)** – Must be unrolled by one agent while another feeds it through a cable tray.  \n\n**Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among five **chemical bottles** (ambient clutter in the break area), the correct **isopropyl alcohol** for cleaning contacts is the only one with:  \n  - **Blue cap** (others are black/red).  \n  - **\"99% Purity\" label** (others say \"Ethanol\" or \"Distilled Water\").  \n  - **Located in the break area fridge** (distractor: looks like a drink).  \n- **Compound Tool Use:**  \n  - **Problem:** The ASUS router’s firmware is corrupted (error LED solid red).  \n  - **Solution:** A **USB recovery drive** (in the cable closet’s \"Misc Parts\" bin) must be loaded with the correct **DD-WRT build** (filename scribbled on a sticky note under the desk).  \n\n---  \n\n### **Atmosphere & Sensory Details**  \n- **Sound:** Hum of cooling fans, intermittent VOIP call drops, and a flickering fluorescent light.  \n- **Smell:** Burnt PCB from an overheated switch in the cable closet.  \n- **Time Pressure:** Wall clock reads 4:58 PM—ISP SLA requires resolution by 5:30 PM.  \n\nThis environment demands **coordinated physical actions**, **distributed knowledge sharing**, and **attribute-based problem-solving**, with ample distractors to test agent precision."}