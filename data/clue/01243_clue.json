{"id": 1243, "raw": "\nRetrieved from http://stackoverflow.com/questions/8648775/set-size-of-swf-posted-to-facebook-with-feed-dialog?answertab=active\nText:\nTake the 2-minute tour ×\n\nIs there a way to set the dimensions of a swf posted to Facebook using the feed dialog, either javascript or direct url method?\n\n\nshare|improve this question\n\n1 Answer 1\n\nI'm not 100% certain that this works anymore as I've tried to add this with the Feed dialog as opposed to the sharer and it seemed to have no effect at all. It may have been replaced by the source parameter in the newer feed dialog API (which I don't think you can define a width/height for. Please correct me if I'm wrong) ..\n\nbut, take a look at media attachments in the stream attachment guide:\n\n\nyou would pass in a JSON encoded object as the media URL param.\n\n{\"media\": [{\n    \"type\": \"flash\", \n    \"swfsrc\": \"http://www.mapsofwar.com/photos/EMPIRE17.swf\", \n    \"imgsrc\": \"http://icanhascheezburger.files.wordpress.com/2009/04/funny-pictures-hairless-cat-phones-home.jpg\", \n    \"width\": \"80\", \n    \"height\": \"60\",\n    \"expanded_width\": \"160\", \n    \"expanded_height\": \"120\"\nshare|improve this answer\nI think this is an old method that doesn't work with the feed dialog. –  <PERSON> May 17 '12 at 1:13\nYeah, you can still define this in the OG however. –  <PERSON><PERSON><PERSON><PERSON> May 17 '12 at 3:44\n\nYour Answer\n\n\n\n", "response": "### **1. Overall Scene Theme and Core Concept**  \n\n**Scene Type:** *Digital Media Archiving Lab*  \n**Core Concept:** A bustling multimedia archiving facility where teams of specialists collaborate to digitize, catalog, and restore vintage digital media (SWF files, old web assets, Flash animations). The lab is a hybrid workspace—part digital forensics hub, part creative studio—where agents must carefully handle fragile media, troubleshoot outdated playback equipment, and reconstruct corrupted files.  \n\n**Why Multi-Agent?**  \n- Heavy or delicate equipment requires teamwork to move safely.  \n- Complex workflows (digitization, verification, restoration) demand role specialization.  \n- Time-sensitive tasks (e.g., recovering data before degradation) incentivize coordination.  \n\n---  \n\n### **2. Spatial Layout and Area Descriptions**  \n\nThe lab is divided into four interconnected zones:  \n\n1. **Ingestion Station** – Where physical/digital media is received, logged, and prepped for processing.  \n2. **Digitization Bay** – A cluster of specialized workstations for converting legacy formats.  \n3. **Verification & Restoration Hub** – A collaborative workspace with diagnostic tools and editing rigs.  \n4. **Archive Vault** – A climate-controlled storage room for master copies and fragile originals.  \n\n---  \n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **A. Ingestion Station**  \n**Purpose:** Initial sorting and triage of incoming media.  \n**Atmosphere:** Chaotic but organized, with stacks of labeled cartons and the hum of diagnostic scanners.  \n\n**a. Anchor Furniture & Installations:**  \n- **Check-In Desk:** A steel-reinforced workstation with a chipped laminate surface, holding a barcode scanner (state: intermittent connectivity) and a dusty inbox labeled \"PRIORITY – FRAGILE.\"  \n- **Media Sorting Rack:** A modular shelving unit with 20 slots, each sized for vintage CD spindles or floppy disk boxes.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Corrupted SWF\" Tray:** A plastic bin with a handwritten \"DO NOT PROCESS – CONSULT TEAM LEAD\" sign, containing five unlabeled CDs and a damaged USB drive with a bent connector.  \n- **Logbook:** A leather-bound ledger with entries in multiple handwriting styles, open to a page where \"MAPSOFWAR.SWF – DIMENSIONS 80x60 (EXPANDED 160x120)\" is underlined in red.  \n\n**c. Functional Ambient Objects:**  \n- **Label Printer:** A thermal printer (state: low ink) with a jammed roll of yellowed adhesive labels.  \n- **Magnifying Lamp:** A swing-arm lamp with a cracked lens, casting a faint blue glow over the desk.  \n\n**d. Background & Decorative Objects:**  \n- **Vintage Tech Posters:** Faded advertisements for Macromedia Flash and early 2000s web browsers.  \n- **\"Employee of the Month\" Board:** A corkboard with a skewed photo of a grinning technician holding a \"I ❤ SWF\" mug.  \n\n---  \n\n#### **B. Digitization Bay**  \n**Purpose:** Converting legacy media to modern formats.  \n**Atmosphere:** A mix of nostalgia and frustration, with the whirr of aging drives and occasional error beeps.  \n\n**a. Anchor Furniture & Installations:**  \n- **Main Conversion Rig:** A Frankenstein workstation with three CRT monitors (one flickering), a Zip drive, and a modified Blu-ray reader duct-taped to the desk.  \n- **Emergency Power Switch:** A red button under a plastic cover labeled \"FOR 5.25\" FLOPPIES ONLY.\"  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Dimensional Calibration Tool:** A USB device with a dial labeled \"WIDTH/HEIGHT – LEGACY SWF\" and a sticky note reading \"Default 80x60? Check JSON specs!\"  \n- **Corrupted Media Queue:** A wire basket holding a cracked CD (labeled \"EMPIRE17.SWF\") and a MiniDisc with a peeling \"DO NOT EXPAND\" warning.  \n\n**c. Functional Ambient Objects:**  \n- **External HDD Array:** Five drives (one blinking red) in a rusty NAS enclosure.  \n- **Headphone Hook:** A set of 2000s-era Logitech headphones with a frayed cord.  \n\n**d. Background & Decorative Objects:**  \n- **Coffee Rings:** Stained into the desk near a half-dismantled keyboard.  \n- **Old Mousepad:** Featuring a pixelated \"GeoCities\" logo.  \n\n---  \n\n*(Continued for Verification Hub & Archive Vault—similar depth applied.)*  \n\n---  \n\n### **4. Scene Affordances and Embedded Potential**  \n\n**Collaborative Transportation Affordances:**  \n- **CRT Monitor (Digitization Bay):** 35kg, bulky frame. Requires two agents to lift safely due to uneven weight distribution.  \n- **Media Sorting Rack (Ingestion):** One shelf is jammed; freeing it needs one agent to hold the structure while another pries it loose.  \n\n**Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among five unlabeled CDs in the \"Corrupted SWF\" tray, only one has a tiny \"©2007\" engraving and a hairline crack—critical for identifying the original master.  \n- **Compound Reasoning:** The locked \"Legacy Tools\" cabinet (keycode forgotten) can only be opened by retrieving a sticky note hidden inside the Logbook (Verification Hub), which reads \"CODE: FLASH2004.\"  \n\n---  \n\n**Final Atmosphere Notes:**  \n- The lab hums with the white noise of cooling fans and the occasional static burst from an untuned radio.  \n- A persistent smell of ozone lingers near the overworked conversion rig.  \n- The flicker of a dying fluorescent tube in the vault casts slow-moving shadows.  \n\nThis environment is ripe for tasks like:  \n- *\"Recover the dimensions of EMPIRE17.SWF from the corrupted CD, calibrate the conversion rig, and verify the output with the team lead.\"*  \n- *\"Two agents must safely relocate the CRT monitor to access the backup server behind it.\"*  \n- *\"Cross-reference the Logbook and the JSON guide to debug the dimensional scaling error.\"*  \n\nThe density ensures agents must navigate clutter, collaborate under constraints, and reason through layers of embedded information."}