{"id": 1257, "raw": "\nRetrieved from http://www.courant.com/la-fi-tn-zerg-rush-easter-egg-google-20120427-story.html\nText:\n\n\n<PERSON>erg <PERSON> Easter egg and other great time wasters from Google\n\n\n\n\nBut this isn't just a sit back and watch Google treat -- this is an interactive game. Google has given you the power to  destroy the Os by clicking furiously on them with your mouse.\n\n\n\nGoogle is nothing if not a good sport.\n\nSo what's the story behind the game? A Google spokesperson emailed us the following statement:\n\n\nFor those who need help understanding that--a \"n00b\" is a person who is new to a game and doesn't know what they are doing, \"pwned\" means getting dominated by an opponent and \"GLHF\" means Good Luck Have Fun.\n\nAs for why this game is called Zerg Rush, n00bs might like to know that Zergs are an alien race of insect-like creatures from the game StarCraft that can mass-produce offensive units in a very short time -- overpowering their opponents by virtue of their numbers.\n\nThis is not the first time that Google has provided the working people of the world a tantalizing time waster.\n\nBack in December, people who typed the words \"Let It Snow\" into Google watched tiny snow flakes fall on the screen, which subsequently fogged up -- just like a car window in a snow flurry. You could trace shapes and words with your mouse, or click the \"defrost\" button to clear the screen. Unfortunately, that one is no longer working.\n\nBut you can still play a virtual online guitar and record your musical experiments through Google's Les Paul Guitar doodle, which has become a stand-alone page\n\nAnd the totally playable Pac-Man doodle is also still online for some good old fashioned gaming fun.\n\nIf you don't want to be tempted by more time wasters, consider treating yourself to just one Google joke. For example, if you type the word \"recursion\" into the search engine, Google will ask you if you meant \"recursion.\"\n\nGet it?\n\n\nLet It Snow! And five other super-fun Google tricks\n\nViral video: Girl ages 12 years in 2 minutes, 45 seconds\n\nWho owns all your data in the new Google Drive Cloud?\n\n\nCopyright © 2014, Hartford Courant\n", "response": "### **Overall Scene Theme and Core Concept**  \n**Scene Type:** *Abandoned Cybernetics Research Lab*  \n**Core Concept:** A high-tech underground research facility, hastily evacuated after a catastrophic incident involving experimental swarm robotics. The lab is littered with dormant or malfunctioning \"Zerg-like\" nano-drone prototypes, scattered research notes, and remnants of a failed containment protocol. The environment is inherently collaborative due to multi-room challenges (e.g., restoring power, sealing breaches, retrieving encrypted data) that require simultaneous actions by multiple agents.  \n\n**Why Multi-Agent?**  \n- Heavy machinery (server racks, sealed containment pods) requires coordinated movement.  \n- Security systems (locked doors, biometric scanners) demand simultaneous input.  \n- Swarm drones reactivate unpredictably, forcing split-second collaborative defense.  \n\n---\n\n### **Spatial Layout and Area Descriptions**  \n1. **Main Research Chamber** – Central hub with a shattered observation window, flickering holographic displays, and a half-disassembled drone hive.  \n2. **Containment Airlock** – A pressurized chamber with a stuck door, leaking coolant pipes, and a damaged control panel.  \n3. **Server Room** – Overheated server racks buzzing with residual power, a floor littered with fried circuit boards.  \n4. **Storage Closet** – Crowded with spare drone parts, hazmat suits, and a locked supply cabinet.  \n5. **Break Room (Incidental Space)** – Coffee-stained manuals, a broken microwave, and a whiteboard with scribbled equations.  \n\n---\n\n### **Detailed Area-by-Area Inventory**  \n\n#### **1. Main Research Chamber**  \n**a. Anchor Furniture & Installations:**  \n- **Central Drone Hive (3m tall, hexagonal, 500kg):** A partially dismantled nano-drone replication unit, its core exposed with tangled fiber-optic tendrils.  \n- **Observation Deck (steel-reinforced glass, cracked):** Overlooks the chamber; control panel has a dead biometric scanner.  \n- **Heavy Workbench (2.5m long, bolted down):** Scattered with micro-soldering irons and magnifying lenses.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Dormant Swarm Drone (30cm diameter, 8kg):** One of six defective units, marked with red \"CAUTION: ERRATIC AI\" tape.  \n- **Decryption Terminal (locked, requires two keycards):** Displays fragmented logs about \"Zeta Protocol Failure.\"  \n- **Emergency Power Switch (behind a rusted panel):** Needs two agents to pull simultaneously.  \n\n**c. Functional Ambient Objects:**  \n- **Robotic Arm (disabled, missing a servo motor):** Hangs limply over the workbench.  \n- **Flickering Holoscreen:** Displays looping error: \"CONTAINMENT BREACHED.\"  \n- **Gas Leak Detector (active, beeping intermittently):** Reads \"COOLANT LEVELS CRITICAL.\"  \n\n**d. Background & Decorative Objects:**  \n- **Graffiti on Wall:** \"THEY MULTIPLIED TOO FAST\" scrawled in marker.  \n- **Coffee Cup (shattered):** Spilled next to a half-eaten protein bar.  \n- **\"Employee of the Month\" Photo (singed):** Dated two days before the incident.  \n\n---\n\n#### **2. Containment Airlock** *(Example of Collaborative Affordance)*  \n**a. Anchor Furniture:**  \n- **Airlock Door (stuck, 300kg):** Requires simultaneous lever pulls in two corners.  \n- **Coolant Tank (leaking, 1m³):** Valve wheel is jammed; needs wrench from Storage.  \n\n**b. Key Interactive Objects:**  \n- **Biometric Panel (partially melted):** Requires two severed scientist ID cards to bypass.  \n- **Broken Drone (wedged in door tracks):** Must be extracted before door opens.  \n\n**c. Functional Ambient:**  \n- **Wall-mounted Fire Extinguisher (unused):** Foam type, expired last month.  \n- **Emergency Lights (strobing red):** Casts eerie shadows.  \n\n---\n\n### **Scene Affordances and Embedded Potential**  \n**Collaborative Transportation Example:**  \n- **Server Rack (150kg, 2m tall):** Must be moved to access a floor vent. Too heavy for one agent; requires coordinated lift.  \n\n**Reasoning & Tool-Use Example:**  \n- **Problem:** Decryption Terminal needs two keycards.  \n- **Solution:** One card is in a dead scientist’s pocket (Main Chamber), the other is inside the locked Supply Cabinet (Storage Closet). Agents must split up.  \n- **Distractor:** Three other non-functional keycards littered in the Break Room.  \n\n**Compound Reasoning Example:**  \n- **Problem:** Coolant leak in Airlock.  \n- **Solution Chain:**  \n  1. Find wrench (Storage Closet).  \n  2. Use wrench to unjam valve (Airlock).  \n  3. Simultaneously hold valve while another agent patches the pipe with sealant (Workbench).  \n\n**Ambient Noise Challenge:**  \n- Identifying the correct drone (for repair tasks) among six, where only one has a **blue corrosion mark** under its thruster—hidden among decorative blue wiring scraps on the floor.  \n\n---  \n**Final Note:** The lab’s density (leaks, drones, locked systems) ensures no single agent can \"solo\" the environment. Decoy objects (e.g., dummy keycards) and time-sensitive threats (reactivating drones) force precise communication and role specialization."}