{"id": 1295, "raw": "\nRetrieved from http://www.nvnews.net/vbulletin/archive/index.php/t-3308.html\nText:\nView Full Version : Mx700\n\n10-26-02, 10:14 AM\nI received my Logitech MX700 cordless optical through FedEx yesterday (like I was telling a few of you in IRC). So here's my input, along with the only critique I have of it:\n\nFirst off, the mouse is extremely comfortable in my hand. That's not surprising to me though. Logitech has always made mice that I found to be that way. Despite the difference in shape from my Dual Optical, both feel equally comfortable for use. Button placement is okay, with the thumb buttons being perfectly placed, and the others (in front of / behind the scrollwheel) being a bit less ideally located. The others are still easily usable, and thanks to the quality of the buttons, they aren't so sensitive that using the wheel with my fingers / palm over them causes them to be hit by accident. The buttons have also are very good in the sense that it's easy to use them and to tell when you've clicked one. The scrollwheel itself moves smoothly, with just enough feedback to let you know how far you've turned it (the 'click' isn't overdone on this mouse, and the wheel isn't stiff).\n\nAs for performance- it works mostly as advertised. I mean, it doesn't lag at all. The tracking and response feels just as good as my Logitech Dual Optical (and a bit better than the MS Intellimouse Opticals that both of these have replaced). I was pleasantly surprised. Also, the buttons are all reassignable, though I would have liked it if they'd allow more than just the F keys and other special keys (iow, you can't assign a letter or a number to the buttons). That's okay too, since my games nearly all allow associating commands with the same buttons the mouse supports.\n\nThe only issue I have with this mouse is the scrollwheel, and specifically in UT2003. The wheel works perfectly without any changes everywhere but in the game. The weird part is that, unlike the other Logitech mice I've used lately, the wheel IS being traced in the game without changing any settings. The problem is that it only works after the mouse has been held motionless for a second or two. Move the mouse, and the scrollwheel input stops. Simple as that. The fact that it works properly elsewhere tells me Logitech needs to get on the ball with the drivers a bit more. Somehow whatever the game (UT2003) is reading for the wheel is being hidden by the movement messages. Thankfully, with a full 8 buttons available for reassignment, I just assigned the forward / back buttons in the thumb area to be backspace and delete, and assigned the alternate next weapon / previous weapon binds in UT2003 to those buttons, so I don't lose any functionality at all. Still, I'd rather have the wheel work right, so I could use it for those functions, and assign specific weapons to the others.\n\nAll in all, it's a keeper. No more cord getting tangled / wrapped up around my keyboard tray, and no cleaning of dirty mouseballs.\n\n10-27-02, 01:56 AM\nOriginally posted by SnakeEyes\n\nI see some things (still) haven't changed. :rolleyes:\n\nLogitech's drivers (or lack thereof), not you. ;)\n\n10-31-02, 09:24 AM\nI just read the review of the MX700 (linked today from the nvnews main page), and the author mentions that the scrollwheel issues don't occur when not using the Mouseware software. A bit further along in the review, he mentions that the application-switching button is the only one that won't function without the Mouseware software. I haven't yet tried this, but it means I have to ask a question-\n\nHas anyone else here used a Logitech mouse (4 button or more) without mouseware, and still had use of other than the three standard buttons and scrollwheel? It seems to me that when I last tried that, the 4th button (thumb button) on my Logitech Dual Optical stopped working, and the MS drivers saw it as only a 3 button scroll mouse.\n\nDo I need to load some special software?\n\n(I never use the app button anyway, but it'd be nice to be able to assign the other buttons in my games and still use the scrollwheel in the normal way..)\n\n10-31-02, 09:31 AM\nwarp2search news page has word that using microsoft intellimouse explorer 3.0 driver solves problems.\n\n11-01-02, 10:15 AM\nAny idea where I can download that revision? I downloaded the MS Intellimouse 4.1 drivers (the ones on MS' site), but after installation, they detect that it's not an MS mouse and simply unload themselves from memory.\n\nBTW, I am using the standard WinXP drivers MS provides, which (ironically) according to the hardware device listing, is actually a Logitech provided driver, and the scrollwheel functions wonderfully. Unfortunately, while the statement that the app-switching button doesn't work when doing this is true, the two thumb buttons are also not working. Even more ironic than that is that the two buttons for scrolling a page (the ones immediately to the front and rear of the wheel) work fine, but aren't programmable. I was able to scroll pages using them, which seems to suggest that by default they emulate the scrollwheel up / down movement, but at some predetermined fixed rate.\n\nIf I can find a driver that works as the standard XP version does, but with support (including programmability, I hope) for the two thumb buttons, this mouse will receive the Snake-Eyes 'Perfect Gaming Mouse' award. As it stands now, it's just got the Recommended for Gamers title. ;)\n\n11-01-02, 11:03 AM\nFirst, thanks for the warp2search tip, thcdru2k. I went there, and then read the reply posts on that news item.\n\nFrom the reading, it appears that some users have had the MS Intellipoint drivers work fine, while others (like me) had no luck. After thinking about it for a few minutes, I had a brainstorm (well, a braindrizzle anyway :D). It's possible the people for which the MS software is working are using the PS2 port to connect their mice.\n\nRight now my mouse is connected on the USB port. One thing I know for sure about the USB port is that devices connected to it identify themselves to the OS / USB driver. This explains why the Intellipoint software unloads itself when I try to use it- it sees that there are no MS mice present, and just quits.\n\nMy idea (which I plan to test tonight) is that the PS2 port DOES NOT identify the hardware connected to it, unlike the USB port. So if I load the MS software after connecting my mouse to the PS2, then tell Intellipoint that my mouse is an Explorer v3.0, it just loads and starts working for the 'Explorer 3.0' on the PS2 port. If this actually works, I'll leave my mouse on the PS2 port for the time being, and just bump up the PS2 rate to at least 125Hz (the rate the MX700 supposedly reports at is 120, but I've seen 125 mentioned in reviews, with values as high as ~148 reported with the rate checker software. I may even set mine to 150 to be safe).\n\nAnyhow, thanks again. I'll report back here with my results, for anyone that's interested. (BTW, I don't know for sure that this is the reason the buttons aren't working yet, it's just a hunch. The reason I'm not certain is that the people over on Warp2search that have theirs working claim to be using MX500 [corded version of the MX700] mice).\n\n11-02-02, 07:54 PM\nOkay, final update.\n\nFirst, the Intellimouse Explorer 3.0 is USB only. So PS2 doesn't work.\n\nSecond, using just standard XP drivers, and not the Explorer stuff, I get all buttons but the app-switch, only none of them are reprogrammable. (Basically, the two thumb buttons function as forward-back in web browsers, and that's all. I only discovered it as I was putzin around without either Intellimouse or Mouseware installed).\n\nLast, I sent an e-mail to Logitech's tech support. It appears that the issue I have with Mouseware and the scrollwheel is a known issue and also that they plan to have the fix in the next set of drivers. (If it IS, W00T! I'd really like that, because other than the scrollwheel quirkiness in UT2003, this mouse is still the best I've used yet, by a long shot. :))\n\nad hoc\n11-02-02, 08:47 PM\nI'm not sure if this will fix your problems, but it did for me...\n\nTry clicking the \"Use MS Office Compabile Scroll Only\" and \"Scroll in Active Window Only\" checkboxes unter the \"Buttons\" tab in the logitech software. See if that doesn't help.\n\n11-03-02, 10:35 AM\nThis time around (I'll explain better in a second..) I don't have to worry about those settings- my mouse wheel is detected no matter whether I have it set or not, because, trust me, I've played with those. The wheel works if the mouse hasn't been moved for at least 3 - 4 seconds, and immediately stops when the mouse is moved.\n\nThe reason I said this time around is that in the past whether the wheel worked or not seemed to be random depending on the time of day, color of sky, amount of air, and local humidity. Changing those settings usually worked to resolve it, but not necessarily enabling them. What setting I needed to fix the problem was just plain random.\n\nSo these drivers are a small step forward from those (hey, at least the wheel seems to be detected for me all the time now..), if only movement of the mouse itself didn't make the wheel seem to stop. ;)\n\n11-07-02, 09:51 AM\nI own the MX300...\n\nI am experiencing the same problem, the scroll wheel just doesn't work in games.\n\nI do have a workaround... It is a pain but it does work. Before starting a game, go into the mouse properties and select \"none\" for the number of lines in the scroll wheel option. This kills the scroll for Windows apps, but for whatever reason the scroll works as it should in games. Just have to remember to set it back to your preferred setting (3 lines, etc.) when you are finished playing.\n\nHere is hoping Logitech addresses this issue, as I love the mouse otherwise.\n\n11-07-02, 10:12 AM\nOriginally posted by kuzujanakis\n\nThis is exactly how I used to \"fix\" issues with my old Logitech MouseMan+ (circa 1998). So it's been 4 years now and they still haven't solved this problem? :rolleyes:\n\n11-07-02, 10:57 AM\nI'll have to try that 'fix'. Anyway, we'll see how they actually address the problem in the next drivers (since I finally got a reply from someone in their support department, unlike in the past with similar issues, and that reply says it's known about, and is being fixed. :p)\n", "response": "## **Scene Concept: Tech Support Workshop for Peripheral Diagnostics & Repair**  \n\n### **1. Overall Scene Theme and Core Concept**  \nThis is a **Tech Support & Hardware Repair Workshop** specializing in diagnosing and fixing computer peripherals—mice, keyboards, headsets, and controllers. The environment is designed for **multi-agent collaboration**, requiring technicians to work together to **test, troubleshoot, and repair faulty devices** while navigating a cluttered, high-tech workspace.  \n\nThe scene is **densely packed with tools, diagnostic equipment, and partially disassembled hardware**, demanding precise coordination between agents to locate components, perform tests, and reassemble devices. The layout encourages **task delegation**, where one agent might handle firmware diagnostics while another retrieves spare parts from storage.  \n\n---\n\n### **2. Spatial Layout and Area Descriptions**  \nThe workshop is divided into **five interconnected zones**:  \n\n1. **Main Diagnostic Bench** – Central workstation with testing rigs, multimeters, and soldering stations.  \n2. **Parts & Inventory Storage** – Shelving units packed with labeled bins of spare components (buttons, scroll wheels, USB connectors).  \n3. **Firmware & Software Station** – A secondary desk with PCs for driver debugging and firmware flashing.  \n4. **Customer Drop-off & Intake Area** – A cluttered counter with tagged devices awaiting repair.  \n5. **Tool Wall & Mobile Cart** – Pegboard with hanging precision screwdrivers, pliers, and a rolling cart for transporting heavy equipment.  \n\nEach area is **visually distinct** but functionally interdependent—agents must move between them to complete tasks.  \n\n---\n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **A. Main Diagnostic Bench**  \n**a. Anchor Furniture & Installations:**  \n- A **3m-long steel workbench** with an anti-static mat, anchored by a **heavy-duty magnifying lamp (adjustable arm, 5x magnification, flickering LED bulb)**.  \n- A **custom-built USB hub test rig** (6 ports, each wired to a separate diagnostic LED panel).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **MX700 Mouse (disassembled)** – Scroll wheel detached, left-click button mechanism jammed.  \n- **Logitech G915 Keyboard (partially dismantled)** – Keycaps removed, exposing sticky mechanical switches.  \n- **Oscilloscope (Tektronix TBS1102B, powered on but displaying noise interference)** – Used for signal tracing.  \n\n**c. Functional Ambient Objects:**  \n- **Soldering station (Hakko FX-888D, idle, sponge dry)**  \n- **Digital multimeter (Fluke 87V, battery low)**  \n- **Assorted screwdrivers (Phillips #00, Torx T5, scattered on the bench)**  \n\n**d. Background & Decorative Objects:**  \n- **\"ESD Warning\" poster (faded, peeling at corners)**  \n- **Coffee mug (chipped, holding loose resistors)**  \n- **Sticky note on the oscilloscope: \"Check USB 5V rail??\"**  \n\n---  \n\n#### **B. Parts & Inventory Storage**  \n**a. Anchor Furniture & Installations:**  \n- **Industrial shelving unit (2m tall, slightly warped middle shelf)** holding **20+ labeled plastic bins**.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Bin #7: \"Mouse Wheels & Encoders\"** – Contains **three MX700-compatible scroll wheels (one cracked)**.  \n- **Bin #12: \"Microswitches\"** – **Omron D2F-01F (correct replacement for MX700) buried under incorrect models**.  \n\n**c. Functional Ambient Objects:**  \n- **Label maker (Brother PT-D210, out of tape)**  \n- **Step stool (wobbly, needed for top shelves)**  \n\n**d. Background & Decorative Objects:**  \n- **Dusty \"Employee of the Month\" plaque (2018)**  \n- **Empty takeout container (smells like old lo mein)**  \n\n---  \n\n#### **C. Firmware & Software Station**  \n**a. Anchor Furniture & Installations:**  \n- **Two-monitor PC setup (main display flickering at 59Hz)** running **Windows 10 (outdated, pending updates)**.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **USB flash drive (labeled \"Logitech Firmware v5.2\", plugged in but not recognized)**.  \n- **External HDD (Western Digital, clicking noise, possibly failing)**.  \n\n**c. Functional Ambient Objects:**  \n- **Mechanical keyboard (Keychron K8, missing Escape keycap)**.  \n- **Coffee stain on mousepad (partially obscuring barcode)**.  \n\n---  \n\n### **4. Scene Affordances and Embedded Potential**  \n\n#### **Collaborative Transportation Affordances:**  \n- **USB Test Rig (150kg, requires two agents to lift)** – Must be moved from the diagnostic bench to the firmware station for recalibration.  \n- **Shelving Unit (top shelf requires step stool + agent to stabilize while retrieving parts)**.  \n\n#### **Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among **five microswitches in Bin #12**, only the **Omron D2F-01F (blue actuator, gold contacts, \"5M\" engraving)** is correct for the MX700. The others are **mislabeled or incorrect models**.  \n- **Compound Tool-Use:** To fix the **flickering oscilloscope**, agents must:  \n  1. **Find a replacement probe (stored in a drawer under the firmware station).**  \n  2. **Adjust the grounding wire (using the insulated screwdriver from the tool wall).**  \n\n#### **Dynamic Problems:**  \n- **The \"Logitech Firmware v5.2\" USB is corrupted**—agents must either:  \n  - **Salvage data from the failing HDD** or  \n  - **Download a fresh copy (requiring network access, but the router is unplugged).**  \n\n---\n\n### **Final Notes**  \nThis workshop is a **high-density, multi-agent puzzle box**, where every object serves a **functional or atmospheric purpose**. Agents must **navigate clutter, diagnose hardware faults, and collaborate under time pressure**—mirroring real-world tech repair scenarios. The environment is **ripe for emergent problem-solving**, from **soldering loose connections** to **debugging driver conflicts**."}