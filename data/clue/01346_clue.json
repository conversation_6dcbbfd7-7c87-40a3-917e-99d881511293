{"id": 1346, "raw": "\nRetrieved from http://stackoverflow.com/questions/10488768/playing-mp4-files-in-firefox-using-html5-video/10488811\nText:\nTake the 2-minute tour ×\n\nI have searched around quite a bit but have not solved my problem.\n\nI have a video tag running as follows:\n\n  src=\"{{ page | video_url }}\" \n  poster=\"{{ page | video_poster_image_url }}\" \n\nI am using Jekyll for the urls. They work fine.\n\nThe site is live at swtizerlandllc.com. Click any video in FF and it shows an image and an X. Chrome and other browsers work fine.\n\nIf you grab the source of a video and load it in a new tab it plays fine. At least it does for me.\n\nI have added:\n\nAddType video/ogg .ogv\nAddType video/mp4 .mp4\nAddType video/webm .webm\n\nto my htaccess file. I suspect that I don't need the .ogv or .webm\n\nI don't understand why loading the video url will play the videos fine but loading the video into a video tag fails.\n\nany ideas?\n\nshare|improve this question\n\n3 Answers 3\n\nup vote 24 down vote accepted\n\nFirefox does not support the MP4 format within its video tag. The main reason why is the royalty fee attached to the mp4 format.\n\nCheck out Media formats supported by the audio and video elements directly from the Mozilla crew or the following blog post for more information:\n\n\nshare|improve this answer\nDirect from the source: developer.mozilla.org/En/… –  <PERSON> May 7 '12 at 20:41\nThanks, I updated my answer with the link! –  <PERSON> Mein May 7 '12 at 20:45\nI have seen this answer around but then why does the video play if you load the path in a new tab? –  TJ Sherrill May 7 '12 at 20:55\nI assume the issue is that in order to support mp4 in their video tag, the Firefox crew would have to pay for a license. Whereas, if the user just puts the link in the browser, they dont have to pay anything. –  Josh Mein May 7 '12 at 21:01\n@TJSherrill, it is probably playing because of a Firefox plugin (like Quicktime). This doesn't mean it will work in a <video> element. –  MPD May 7 '12 at 21:24\n\nFirefox 21 supports MP4 H.264 by default. Yay! Just try this video test - http://www.quirksmode.org/html5/tests/video.html\n\nEDIT: FF21+ only on windows 7+ apparently. https://developer.mozilla.org/en-US/docs/HTML/Supported_media_formats\n\nshare|improve this answer\n\nI can confirm that mp4 just will not work in the video tag. No matter how much you try to mess with the type tag and the codec and the mime types from the server.\n\nCrazy, because for the same exact video, on the same test page, the old embed tag for an mp4 works just fine in firefox. I spent all yesterday messing with this. Firefox is like IE all of a sudden, hours and hours of time, not billable. Yay.\n\nSpeaking of IE, it fails FAR MORE gracefully on this. When it can't match up the format it falls to the content between the tags, so it is possible to just put video around object around embed and everything works great. Firefox, nope, despite failing, it puts up the poster image (greyed out so that isn't even useful as a fallback) with an error message smack in the middle. So now the options are put in browser recognition code (meaning we've gained nothing on embedding videos in the last ten years) or ditch html5.\n\nshare|improve this answer\n+1 \"Firefox is like IE all of a sudden, hours and hours of time, not billable.\" –  Geo Nov 6 '13 at 22:11\nWhich video type works? OGG, or WEBM? –  Jackson_Sandland Nov 16 at 23:44\n\nYour Answer\n\n\n\n", "response": "### **Scene Concept: \"The Abandoned Digital Media Archive\"**  \n\n#### **1. Overall Scene Theme and Core Concept**  \nA forgotten media storage facility, once a bustling hub for digital archiving, now stands in partial decay. The space is filled with outdated technology, stacks of physical media, and remnants of a time when video formats were a battleground for compatibility. The scene is inherently collaborative due to:  \n- **Heavy, bulky equipment** requiring multiple agents to move.  \n- **Complex troubleshooting scenarios** involving mismatched media formats, broken hardware, and missing documentation.  \n- **A layered information system**—some data is stored on physical drives, some on networked servers, some in handwritten logs—requiring coordinated retrieval.  \n\n#### **2. Spatial Layout and Area Descriptions**  \nThe facility is divided into four key areas:  \n\n1. **Main Media Storage Room** – A dimly lit chamber with towering racks of servers, tape reels, and optical disc cases. The hum of failing cooling systems lingers in the air.  \n2. **Format Testing Lab** – A workstation cluttered with playback devices (VHS decks, Blu-ray players, CRT monitors) and diagnostic tools. A chalkboard lists failed format conversions.  \n3. **Abandoned IT Office** – A desk buried under stacks of manuals, sticky notes, and a half-dismantled PC. A whiteboard reads: *\"FIX FF MP4 ISSUE – LAST PRIORITY?\"*  \n4. **Server Closet** – A cramped, overheated space with flickering LED indicators. Dust-coated machines whirr erratically; some are labeled *\"DO NOT POWER DOWN.\"*  \n\n---  \n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **Main Media Storage Room**  \n**a. Anchor Furniture & Installations:**  \n- **Industrial Server Racks (2m tall, 300kg each)** – Some are tilted precariously, held up by improvised supports.  \n- **Steel Media Shelving Units** – Holding labeled boxes of Betamax tapes, Zip disks, and unmarked CD spindles.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Corrupted Master Tape\"** – A VHS cartridge tagged *\"SWITZERLAND LLC – FINAL CUT (DO NOT REWIND)\"*, jammed in a malfunctioning deck.  \n- **Format Conversion Terminal** – A dusty PC with a sticky note: *\"MP4 encoder offline – use Workstation B.\"*  \n\n**c. Functional Ambient Objects:**  \n- **Label Maker** – Out of tape, displaying *\"ERR: RIBBON\"*.  \n- **Overflowing Recycling Bin** – Filled with cracked CD cases and shredded documents.  \n\n**d. Background & Decorative Objects:**  \n- **Framed \"Employee of the Month\" Photo (1998)** – Glass cracked, hanging crookedly.  \n- **Obsolete Tech Posters** – *\"Embrace the Future: HD-DVD!\"*  \n\n---  \n\n#### **Format Testing Lab**  \n**a. Anchor Furniture & Installations:**  \n- **Multiformat Playback Array** – A Frankenstein setup of laserdisc, DVD, and MiniDV players wired to a single CRT.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Test Disc #45\"** – A scratched Blu-ray labeled *\"FF Compatibility Test – FAILS ON FIREFOX.\"*  \n- **Signal Analyzer** – Displaying *\"CODEC NOT FOUND\"* in red text.  \n\n**c. Functional Ambient Objects:**  \n- **USB Hub** – Missing two ports; one has a bent pin.  \n- **Calibration Remote** – Batteries corroded in the compartment.  \n\n**d. Background & Decorative Objects:**  \n- **Coffee Mug** – Stained, holding pens and a screwdriver.  \n- **Outdated Magazine** – *\"Web Video Monthly, June 2012\"* open to an article on MP4 royalties.  \n\n---  \n\n#### **Abandoned IT Office**  \n*(Focus: Information retrieval, tool scavenging)*  \n**b. Key Interactive & Task-Relevant Objects:**  \n- **Locked Filing Cabinet** – Key hidden under a keyboard. Inside: a folder labeled *\"License Agreements – H.264???\"*  \n- **Debugging Notebook** – Last entry: *\"If direct MP4 link works but <video> tag doesn’t, blame plugins??\"*  \n\n**d. Background & Decorative Objects:**  \n- **Sticky Note Vortex** – One reads *\"Call Mozilla??\"* with a doodle of a crying fox.  \n\n---  \n\n#### **Server Closet**  \n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Primary Archiver\" Server** – One drive bay is open, exposing a loose SATA cable.  \n- **UPS Battery Backup** – Beeping intermittently; display reads *\"CRITICAL: 2 MIN REMAINING.\"*  \n\n---  \n\n### **4. Scene Affordances and Embedded Potential**  \n\n#### **Collaborative Transportation Affordances:**  \n- **Server Rack (300kg, 2m tall)** – Requires two agents to stabilize while a third replaces its failing power supply.  \n- **\"Corrupted Master Tape\"** – Stuck in a VHS deck bolted to a workbench; one agent must hold the unit steady while another pries it open.  \n\n#### **Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among five external hard drives on a shelf, only one has:  \n  - A **blue \"ARCHIVE\" sticker**  \n  - **Scratch marks** near the SATA port  \n  - A **handwritten label**: *\"BACKUP – SWITZ LLC\"*  \n  *(Distractor: Three other drives have similar labels but lack the blue sticker.)*  \n- **Compound Reasoning:**  \n  - **Problem:** The Format Conversion Terminal displays *\"MP4 encoder offline.\"*  \n  - **Solution:** A repair manual in the IT Office lists *\"Encoder module requires FW v2.1.3\"*—but the update is on a CD in the Storage Room’s *\"Software\"* box.  \n\n#### **Atmospheric Distractors:**  \n- A **decorative \"Think Different\" poster** partially obscures the server closet’s emergency shutdown instructions.  \n- A **non-functional oscilloscope** in the Lab has blinking lights, mimicking an active device.  \n\n---  \n\n### **Why This Scene Works for Multi-Agent AI**  \n- **Physical Collaboration:** Heavy objects, unstable machinery, and two-handed tasks (e.g., holding + unscrewing) force teamwork.  \n- **Layered Information:** Critical clues are scattered across areas (e.g., a note in the Office references a tool in the Lab).  \n- **Realistic Noise:** Decorative objects (like outdated magazines) mimic real-world clutter, requiring agents to filter signal from noise.  \n\nThis environment is a playground for embodied AI—every object has history, purpose, and potential for chaos."}