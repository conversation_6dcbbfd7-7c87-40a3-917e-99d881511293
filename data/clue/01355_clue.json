{"id": 1355, "raw": "\nRetrieved from http://www.bluesnews.com/cgi-bin/board.pl?action=postmessage&boardid=1&threadid=136529&id=806069&start=0\nText:\n\nUser Settings\nLAN Parties\nUpcoming one-time events:\n\nRegularly scheduled events\n\nMechWarrior Online Trailer\n\n\nPost Comment\n\n6. Re: MechWarrior Online Trailer Nov 6, 2012, 06:02 InBlack\nOk I joined the Open Beta a few days ago. Here are some impressions. Please take note Im a diehard Mechwarrior fan, I have played every Mechwarrior game to death since I discovered the series way back when with Mechwarrior2 so I might be a little bit biased. (Which still remains my favorite, simply because of the grand storyline and the strangeness of the Clans)\n\nFirst the graphics. This is the best looking Mechwarrior game to date. Shit blows up in your face, the cockpits are excellent, <PERSON><PERSON> leave chemtrails, Lasers heat up the enemies armor to a glowing white glow, mechs shake and rattle when struck you name it the Cryengine3 does it pretty well.\n\nThe gameplay though leaves a lot to be desired. Sure the game is a beta but they are taking people's money so it IS a valid discussion point. IMO its extremely simple and there is not really much to do other than duke it out TvT. There is one game mode assault which features 8 mechs per team but it basically boils down to a WoT style kill the enemy before they kill you. Teamplay is rather important and good teams make short work of the enemy but more often than not the makeup of the specific teams is much more important. Newbie mechwariorrs who dont want to dish out cash need to grind out the ingame currency C-bills to purchase mech variants which can be customized to your hearts content but up until that point you are basically stuck playing the very crappy 'stock' mechs which basically means you are more or less a target practice dummy on the battlefield. The grind doesnt seem to be excessive though which is good because it only means I need to LOSE 100 matches before I can buy a decent mech variant which can be customised.\n\nCombat mechanics are ok, people complain of lag but its not that noticable (at least for me) and I usually ping around 150 from Europe.\n\nWhat is horribly noticable is the mech clipping. As far as I can tell you clip through friendly and enemy mechs 100%. This is a HUGE bummer, as it gives an unfair advantage to light mechs who dont have to manuever as much and DFA (death from above) is obviously not working as intended.\n\nAll in all it feels more like a tech demo than a full game, and the strategy in 8v8 matches is very limited. Basically the team that has the better equiped mech wins always hands down.\n\nThe longevity of the game depends on them adding more mechs, factions, and improving the combat mechanics (such as turning off clipping and adding more physics effects) but I cant see that happening any time soon.\n\n\nAvatar 46994\nI have a nifty blue line!\nReply Quote Edit Delete Report\nLogin Email   Password Remember Me\nForgotten your password? Click here.\n          Email me when this topic is updated.\n\nSpecial Codes\n\n  • b[bold text]b\n  • i[italic text]i\n  • u[underline text]u\n  • -[strikethrough text]-\n  • c[code text]c\n  • +[bullet point]+\n  • q[quote text (indented)]q\n  • [url=Link]text[/url]\n  • r{red text}r\n  • g{green text}g\n  • b{blue text}b\n  • m{maroon text}m\n\nForum Rules\n\n  2. Ethnic slurs and homophobic language will not be tolerated.\n\n\nBlue's News logo\n", "response": "### **1. Overall Scene Theme and Core Concept**  \n**Theme:** A **Mech Hangar & Simulation Lab** in a futuristic mercenary outpost, repurposed from an old industrial warehouse.  \n\n**Core Concept:**  \nThis is a high-stakes environment where teams of mech pilots, engineers, and support staff collaborate to maintain, customize, and simulate battle scenarios for their war machines. The scene is inherently multi-agent due to:  \n- **Heavy machinery** requiring coordinated handling (e.g., mech limbs, armor plating).  \n- **Complex workflows** (e.g., diagnostics, weapon calibration, mission planning).  \n- **Time pressure** (e.g., prepping mechs before a live drop).  \n\nThe space blends **industrial grit** (grease stains, exposed wiring) with **cutting-edge tech** (holographic displays, diagnostic AIs).  \n\n---  \n### **2. Spatial Layout and Area Descriptions**  \nThe hangar is a **large, open space** with segmented zones:  \n\n1. **Mech Bays (x3)** – Each bay holds a partially disassembled mech in varying states of repair. Overhead cranes and hydraulic lifts dominate.  \n2. **Armory & Tool Storage** – Reinforced lockers for weapons, modular armor, and specialized tools.  \n3. **Simulation Deck** – A raised platform with VR rigs, tactical maps, and a malfunctioning holographic projector.  \n4. **Engineering Pit** – A sunken workspace strewn with diagnostics equipment and spare parts.  \n5. **Break Area** – A cluttered corner with a coffee maker, lockers, and a bulletin board covered in mission briefs.  \n6. **Server Closet** – A cramped room humming with networking gear and overheating simulation servers.  \n\n---  \n### **3. Detailed Area-by-Area Inventory**  \n\n#### **A. Mech Bay 1 (Heavy-Class \"Atlas\" Mech)**  \n**a. Anchor Furniture & Installations:**  \n- **Hydraulic repair gantry** (3-ton capacity, currently suspending the mech’s detached left arm).  \n- **Heavy-duty workbench** (bolted to the floor, stained with coolant fluid).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Detached mech arm** (400kg, requires two agents to lift; socket connectors sparking intermittently).  \n- **Armor plating crate** (sealed, labeled \"FERRO-FIBROUS – FRAGILE\").  \n- **Diagnostic terminal** (screen flickering with error: *\"Servo Actuator #3 OFFLINE\"*).  \n\n**c. Functional Ambient Objects:**  \n- **Tool cart** (wrenches, plasma cutters, half-empty grease tubes).  \n- **Coolant pump** (hissing faintly, pressure gauge in the yellow zone).  \n\n**d. Background & Decorative Objects:**  \n- **Graffiti on the wall** (\"NOVA CATS RULE\" crossed out, replaced with \"DEATH TO CLANS\").  \n- **Dented coffee mug** (sitting atop a stack of outdated technical manuals).  \n\n---  \n#### **B. Simulation Deck**  \n**a. Anchor Furniture & Installations:**  \n- **Holo-table** (projecting a glitchy 3D terrain map of the \"Canyon Network\" battlefield).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **VR headset** (left lens cracked, trailing wires).  \n- **Tactical uplink console** (showing \"ERROR: SERVER CONNECTION LOST\").  \n\n**c. Functional Ambient Objects:**  \n- **Mission whiteboard** (scribbled with stratagems: *\"Lure lights into choke pt. – WATCH FOR LRM SPAM\"*).  \n- **Radio headset** (crackling with static, no signal).  \n\n**d. Background & Decorative Objects:**  \n- **Empty energy-drink cans** (strewn near a chair with a torn seat).  \n- **Outdated faction poster** (\"JOIN HOUSE DAVION TODAY!\").  \n\n---  \n#### **C. Armory & Tool Storage**  \n**a. Anchor Furniture & Installations:**  \n- **Weapons rack** (holding a **Gauss Rifle** [requires two agents to lift] and **Medium Lasers**).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Locked ammo crate** (needs a keycard; inside: **LRM-20 ammo bins**).  \n- **Calibration toolset** (scattered, missing the **hex-key adapter**).  \n\n**c. Functional Ambient Objects:**  \n- **Parts bins** (labeled but disorganized: \"MYOMER BUNDLES – USED\").  \n\n**d. Background & Decorative Objects:**  \n- **Faded safety poster** (\"REPORT PILOT INTOXICATION\").  \n\n---  \n### **4. Scene Affordances and Embedded Potential**  \n\n#### **Collaborative Transportation Affordances:**  \n- **Detached Mech Arm (400kg)** – Requires two agents to lift due to weight.  \n- **Gauss Rifle (180kg)** – Bulky shape necessitates teamwork to mount on a mech.  \n\n#### **Reasoning and Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among five coolant tanks, only **one** has a **red valve** (others are blue), is **half-full**, and is labeled **\"CORROSIVE – USE GLOVES.\"**  \n- **Compound Reasoning:** To fix the **glitching holo-table**, agents must:  \n  1. **Reset the server** (in the overheated server closet).  \n  2. **Replace a fried circuit board** (stored in Engineering Pit).  \n\n#### **Dynamic States for Problem-Solving:**  \n- **Mech Leg Joint** (stuck in \"locked\" position; needs hydraulic jack to free).  \n- **Server Rack** (overheating; requires coolant refill).  \n\n---  \n**Final Note:** This environment thrives on **chaotic realism**—every object has a purpose, but the clutter demands precision. Agents must navigate **physical collaboration**, **tool dependencies**, and **hidden solutions** amid the grime and glory of a mercenary’s workshop."}