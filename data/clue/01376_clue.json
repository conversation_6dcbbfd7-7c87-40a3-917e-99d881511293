{"id": 1376, "raw": "\nRetrieved from http://www.moddb.com/mods/morrowind-rebirth/features/changelog7\nText:\n\n\nReport article RSS Feed Changelog (very outdated)\n\nA very messy changelog with a lot of the changes missing, I guess it's better than nothing, right? I will TRY to update it sometime soon.\n\nPosted by trancemaster_<PERSON> on May 10th, 2013\n\nVanilla Fixes\n* Gave the Imperial Guards in Gnisis Imperial Broadswords instead of Imperial shortswords. This due to thier low skill in using shortswords.\n* Added Centurion Archers to Vvardenfell's ruins.\n* Guild chest added to Caldera Mages Guild.\n* Added faction & rank to Imperial Archers\n* Added missing attacksound to Ascended Sleepers.\n* Some creature spawns have been relocated to avoid clipping issues.\n* Removed Bonemold Tower Shield from Hlaalu Sharpshooters.\n* Imperial Studded Leather/Imperial Silver Cuirass/Imperial Dragonscale Cuirass now counts as an Legion Uniforms'.\n* Changed the name of \"silver_staff_dawn_uniq\". This staff's real name is Staff of the Silver Dawn, though it appears as Silver Staff in the game, corrected.\n* Changed the name of \"silver spear_uvenim\". This staff's real name is <PERSON><PERSON><PERSON>'s <PERSON> Spear, though it appears as <PERSON> Spear in the game, corrected.\n* In 'The Plan To Defeat Dagoth Ur' (bk_vivecs_plan), the line that reads \"A mortal Kagrenac may then be destroyed by mundane means\" should read Dagoth Ur instead of Kagrenac.\n* A door in the Balmora Council Club were replaced by \"in_hlaalu_door\" instead of \"rent_balmora_council_door\".\n* A load of Silver Weapons weren't properly marked as ”Silver Weapons.\n* Skaal Guards' will now wear Nordic Shields.\n* Draugr now counts as undead, not as creatures.\n* Redoran Guards (female), will now greet you properly.\n* Ebony/Daedric Towershields are now both heavier (5 ibs) and have a slightly higer AR (+5) then their regualar counterparts.\n* Blessed Tower Shield now uses the correct \"bodypart\"\n* Most NPCs' in the Imperial Guard Garrison, Ebonheart didn't have any weapons to fight with, or they used the wrong type of weapon compared to their assigned class.\n* All House Dagoth members now have a random chance to drop Ash Salts.\n* \"Borwen\", removed faction: \"Ashlanders\".\n* \"Shat gro-Shazog\" & \"Orbul gra-Lumob\" have been given weapons to fight with.\n* \"Gulfim gra-Borbul\" in Gorak Manor had no pants.\n* \"Rulfim gra-Ogdub\", a prisoner in Buckmooth Legion fort had no clothes.\n* Sirollus Saccus were supposed to provide training options, now he does.\n* Black Arrow, Volume 1 -> Black Arrow, Volume I\n* misc_dwrv_artifact30, weight 0.01 > 0.10.\n* Horned Lily will now respawn.\n* Holly Bush will now respawn.\n* Changed Cell name (-6,17): “Ashlands Region” to “Druscashti”, as described in-game.\n* \"Gah julan\" and \"Teegla\" are now wearing slave bracers.\n* Removed \"auto-calculate\" from \"Ungeleb\" in Mournhold, Magic Shop.\n* Scroll Of Element Frost/Fire had wrong font size, corrected to \"2\".\n* Removed auto-calculate from Sjoring Hard-Heart since it messed up his stats.\n* \"Dul gro-Dush\" in Gnisis, class warrior > guard.\n* \"Yakov\" (the Altmer slave in the Suran Slave Market) didn't have a slave script attached.\n* \"Staada\", a golden saint encounter were improperly set as a creature instead of daedra. Also marked \"corpse persist\".\n* Removed soul value from all Spider Centurions.\n* \"Lich Barilzar\" had a soul value of 30, which didn't seem right since he's such a powerful foe. 300 seem like the correct number.\n* Spiggans now have a chance to drop Heartwood.\n* Netch_Giant_UNIQUE, scale 1.0 > 1.2.\n* Added missing spells/ingredients/abilities to several creatures.\n* Adamantium pauldron, enchantment 100 -> 30.\n* Iron Cuirass, health 2000 > 200.\n* Netch Leather Tower Shield, AR 5 > 6 / health 100 > 120.\n* Corrected a few weapons that used the wrong enchantment.\n* Stendars Hammer, damage 100 > 160.\n* King's Oath , fire damage self > touch.\n* Hackle-Lo Leaf, value 30 > 3.\n* Adamantium Ore, weight 50 > 5.\n\n* Exclusive Frost Shield Potion, duration 30 > 60.\n* Spoiled SlowFall Potion, slowfall magnitude 1 > 10.\n* Fixed an enchantment error with the Ring of Equity where the player wouldn't be able to take use of the rings powers.\n* Variner's Ring, weight 1 > 0.10.\n* Expensive shirt Mournhold, value 1 > 15.\n* Necromancers Amulet, value 240 > 2400.\n* Heartfang, value 120 > 1200.\n\n* Ten-Tongues Weerhat (Mournhold) will no longer sell \"exquisite_shirt_01_rasha\", which is a quest item.\n* You will now be able to talk to the Ash Vampires (as intended by the developers) before you face them in battle.\n* Her Hand's Shield had diffrent AR value than the rest of the set, corrected.\n* Royal Guard boots and greaves had diffrent AR value than the rest of the set, corrected.\n* Dwemer Coherer is no longer sold at Mebestian Ence's shop.\n* \"Brallion's Exquisite Ring\" value 40 > 240 to match the rest of the exquisite rings.\n* Corrected and balanced several weapon enchantments.\nNormal Flame/Shard/Viper/Spark Enchantment: 1-4 damage > Iron Weapons\nCruel Flame/Shard/Viper/Spark Enchantment: 2-8 damage > Steel Weapons\nDire Flame/Shard/Viper/Spark Enchantment: 4-10 damage > Silver Weapons\nWild Flame/Shard/Viper/Spark Enchantment: 6-12 dmage > \"Wild\" Weapons\n* Redoran Guards now wear a Redoran Shield and a Steel Katana.\n* Sorkvild the Raven will now actually wear the Masque of Clavicus Vile in battle.\n* The \"Chiding Cuirass\" held no charge, so it was impossible to use the enchantment.\n* Dorisa Darvel were supposed to have books about the Nerevarine, but she didn't.\n* Dagoth Endus had no sounds associated to him.\n\n* The Nord Leg had a missing enchantment.\n* \"Ex_co_ship_trapdoorb\" in Frostmoth docks have been replaced with \"Act_Ex_DE_ship_trapdoor\". \"Ex_co_ship_trapdoorb\" is scripted colony door and a part of Bloodmoon's mainquest.\n* Gondolier's Helm were changed from ”Medium Armor” to ”Light Armor”.\n11 bows and added a resonable amount of arrows.\n* Some pieces of the Imperial Chain Armor set had the wrong AR value, fixed from 20 > 12.\n* Removed\nshields from High Fane Ordinators.\n* A stair in Ald-Ruhn, Guild of Mages,\nwas missplaced.\n* The Adamantium Axe were misspelled as \"Admantium\".\n* Large Dwemer Goblet were misspelled as \"Dewmer\".\n* The Slavepods in Sadrith Mora wasn't\nlocked as supposed to.\n* Added a shrine to Molag Mar, Temple.\n\n* Moved a lamp at the Redoran Smith, Vivec. Sometimes the player would get stuck in it upon entry.\n* Sorkvild the Raven's body will now persist, as he's the bearer of the Mask of Clavicus Vile.\n* Fixed a travel-marker in Sadrith Mora that leaves you stuck.\n\n* Junal-Lei in Pelagiad had no AI-package.\n* Angoril in Pelagiad had no AI-package.\n* Madres Navur in Pelagiad had no AI-package.\n* \"Bound_Helmet\" changed to \"Bound Helmet\"\n\nCreature changes\n* Many creatures have been buffed, especially Daedras, to make the game a greater challange for high level characters.\n* All blighted creatures now have unique textures. This will make them easy to spot and avoid if necessary. Credit goes to ”PeterBitt”.\n* Some significant changes to Atronachs.\n- Removed \"Reflect 20\" from all Atronachs'.\n- Atronachs' now emit light.\n* Removed ingredients from summoned creatures.\n* Skeleton Archers will carry 30 arrows instead of 60.\n* Alit's now use their ability \"Alit Bite\".\n* Added missing diseases to several creatures.\n\nSetting changes\n* Elemental shields were supposed to deal damage to enemies that hit you in melee combat, now they do.\n* Players minimum speed is now a bit higher at lvl 1. Also reduced the max-speed so the player can't run super-fast at higer levels.\n* 20% less chance to be knocked down during fights.\n* NPC's will have much lower disposition if you have your weapon drawn during conversations (-15 points).\n* Traders will be more suscpicious to you. You need a high personalty rating to get better prices.\n* Your disposition will be harder to raise through bribing NPC's.\n* NPC's won't greet you from a distance. You will now have to stand close to them.\n* Doubled the amount of money you have to pay for committing a crime.\n* 50 % chance to recover projectiles from bodies (From 25 %).\n* You will no longer be able to survive jumping of buildings or cliffs.\n* Guild cheasts will respawn monthly (From 3 months).\n* Vendors will resupply their stash of gold every 72 hours (From 24 hours).\n* Training is more costly (2x vanilla).\n* 25 % harder to pick locks.\n* Running will now drain 0.5 points less each second while running.\n* Enchanted items recharge 50 % slower\n* NPC's magica-pool will be a big larger so they actually will be able to throw the high level spells in their spell-list.\n* It's now more costly to remove your bounty through the Thieves Guild Services.\n* It's now harder to pick locks and disarm traps, some traps are also more deadly.\n\nNew Features\n* You will now need Hospitality Papers to receive any services in Sadrith Mora from the Telvanni,\n* You will now need a Muckshovel to \"harvest\" muck.\n* You will now need a Minerspick to get glass, ebony & adamantium\n* Now it will snow in the Hirstaang Forest (20% chance).\n* You will now encounter Frost Atronachs' in Solstheim, Durzogs' in the West Gash, Centurion Archers in Dwemer Ruins and Liches In tombs\n* New Imperial Travel Agents that will take the player to various destinations around the Island. They will also provide the player with diffrent types of maps.\n* Shops and some other places will now close night-time between 8 pm/8 am.\n* I've renamed the potions to make them stack better, it's now way easier to find that specific potion in the heat of battle.\n- Common potions: Potion of/Name/Quality,\nExample:(Potion of Light:Bargain)\n- Restore/Fortify Potions: \"Restore|Fortify/Name/Quality,\nExample:(Restore Fatigue:Bargain, Fortify Health:Bargain)\n- Resistance Potions: Name/Quality,\nExample:(Fire Resistance:Bargain)\n* All types of guards will now take use of standard health potions in battle.\n* You can now buy \"portable\" campfires and bedrolls from various traders around the Island.\n* You'll now take damage if standing in a fire or really close to it.\n* All \"practice dummies\" will now be animated.\n\nWeapon/Armor changes\n* Tweaked the \"reach\" for all weapons. A spear should naturally cover a bigger radius than for example a sword.\n* All enchanted weapons and armor now have the same stats as their vanilla counterparts (enchantments should be the only difference).\n* Changed the name of a Nordic Silver Battle Axe to \"Heartfang\", as it was supposed to be an exclusive weapon.\n* The Bonemold Long Bow was seriously overpowered with 400 enchantment points, changed it to 80.\n* The Dwemer mace \"Clutterbane\" had an error which made it impossible to use its enchantment.\n* Gave \"Volendrung\" an enchantment similar to the one in Daggerfall.\n* Gave \"Auriel's Shield\" an enchantment similar to the one in Daggerfall.\n* Gave \"Auriels Bow\" an enchantment similar to the one in Daggerfall.\n* Gave \"Foeburner\" a fitting enchantment dealing fire damage.\n* Stalhrim armor pieces and weapons were refered to as \"Ice\" in-game, changed to \"Stalhrim\".\n* Dwemer weapons were refered to as \"Dwarven\" in-game, changed to \"Dwemer\".\n* You will now be able to \"hit\" ghosts using Adamantium weapons.\n* Made some changes to medium armor, which in turn should make medium armor more useful.\n- Dwemer AR from 20 to 35.\n- Adamantium AR from 40 to 50.\n- Stalhrim AR from 50 to 55.\n* New unique meshes for magic weapons.\n\n* Tweaked most enchantments to either make them more useful or less powerful. Also tweaked the cost to cast these enchantments.\n* Tweaked most spells to either make them more useful or less powerful. Also tweaked the cost to cast these spells.\n* Reduced the value for most weapons/armor and misc items to reduce the overflow of money in the game.\n* Most special characters/creatures and other types of enemies are now much stronger than in vanilla Morrowind.\n* Summoned creatures have had their soul value set to 0. This mean that the player will no longer be able to exploit summons to get valuable soul-gems.\n* Exclusive ingredients like pearls are now less likely to appear in the gameworld.\n* Player made potions are now 50 % less valueable.\n* Reworked leveled-lists so it won't be as easy to get hold of Dwemer, Ebony, Glass & Deadric items/armor/weapons.\n* Reduced the amount of arrows carried by Imperial Archers, Telvanni Sharpshooters, Hlaalu Marksmen etc.\n* Hundreds of doors, chests, desks etc have been locked in order to make it harder for the player to gain access to valuable items.\n* Hlaalu, Telvanni and Redoran sharpshooters are now equipped with the traditional Bonemold longbow and Bonemold arrows.\n* Many creatures, undead or humanoids had the wrong spells or resists which made them either too strong or too weak.\n* You are no longer able to enchant items, armor & weapons with Chameleon, Invisibility & Sanctuary.\n* New balanced equipment for Dark Brotherhood Assassins:\n* Brown Bears, Guars, Mudcrabs, Cliffracers and Grey Wolves will no longer attack the player on sight, although they might attack if you get to close to them.\n* Tweaked enchantment points for clothes to make them more useful for mages.\n* Removed some very expensive soul gems from Balmora, Guild of Mages and replaced them by empty counterparts.\n* You could find not only one, but two \"Sword of the White Woe\" in Balmora, I removed one of these. Also moved the remaining one to a more secure location where the guard can see it.\n* Replaced the patroling Ordinators in all great house vaults with stationary ones. This will make it much harder to steal what's inside.\n* Removed the Limeware platter from the Census Office, replaced with a Redware platter (no more exploiting!)\n* Guild chests have less potions (cheap ones removed). Also removed the arrows/bolts from the Fighter Guild's Supply Chest, replaced them with something more apporopriate.\n* Some skills are now a bit harder to raise, especially weapon skills, alchemy and athletics.\n- Alchemy: I cut the progression rate by 75 %. I feel that Alchemy is probably the easiest spell to raise in Morrowind.\n\nNew Areas\n\nBalmora Underworld, by \"Fulgore\"\nGet lost within the depths of Balmora's Underworld. Take a walk through the sewers: let the Thousand Lanterns Market take your breath away or why not explore the abandoned canals? Witness criminal factions fighting against each other and common folks going on about their daily lives. Discover the way to access the massive cave system that lies deep below Balmora. If you're lucky enough, you may live to witness Bthumynal, the legendary lost Dwemer ruin and its secrets within. An epic adventure awaits you Outlander.\n\nOutpost Renius, by \"Fulgore\"\n\"Somewhere in the Ascadian Isles, not too far from Pelagiad, on a hill overlooking Lake Amaya, there's an old small keep known as 'Outpost Renius'. Some months ago all contact was broken with the troops garrisoned there, a patrol was dispatched to check the situation. Everyone, from the pettiest prisoner to the highest ranking official, was found dead. After hearing sinister voices and steps, the place was sealed and abandoned. Do you have what it takes to venture inside and discover what happened to the people in there?\"\n\nDren's Hidden Caverns, by \"Fulgore\"\n\nVvardenfell's bad guy just got worse. Explore what goes on in the caves below Dren Plantation.\n\nThe Deadlands\nTravel through a Daedra Gate and face the ultimate challange in the Daedra Realm!.\n\nNew Armor\n* Trollbone Set\n* Colvonian Gloves\n* Morag Tong Armor\n* Adamantium Towershield\n* Adamantium Round Shield\n* Skull Shield\n* Imperial Templar Shield\n* Imperial Captains' Helmet\n* Royal Guard Shields\n* Thieves Guild Armor\n* Redguard Headwraps\n\nNew Weapons\n* Adamantium Dagger\n* Falmer Longsword\n* Falmer Shortsword\n* Silver Katana\n* Silver Battle Axe\n* Silver Longbow\n* Deadric Longspear\n* Dwemer Longspear\n* Dwemer Staff\n* Dwemer Dagger\n* Dwemer Bolt\n* Ebony Halberd\n* Ebony Dagger\n* Imperial Dagger\n* Imperial Longsword\n* Glass Spear\n\nNew Artifacts\n* Voidguard\n* Queen of Bats\n* Face Of Kagrenac\n* Emperor's Defence\n* House Hlaalu Royal Shield\n\nNew Creatures\n* Armored Dwemer ghost\n* 5 Riekling variations\n* Daedra Seducer\n* Fire Clannfear\n* Clannfear Runt\n* Flesh Atronatch\n* Goblin Shaman\n* Draugr Berserker\n* Draugr Deathlord\n* Stunted Scamp\n* Swamp Troll\n* Frost Giant\n* Beholder\n* Green Slime\n* Treant (Unique)\n* Frost Monarch\n* Hill Giant.\n* 3 new Dremora Lords\n* 4 new skeletons\n\nNew Ingredients\n* Wickwheat Muffin\n* Atro Flesh\n* Nether Salt\n* Ore Essence\n* Scrap Iron\n* Troll Slime\n* Telvanni Resin\n* Void Essence\n* Wind Salt\n* Guar Meat\n\nNew Books\n* Forging A Heavy Duty Blade\n* Tale of The Devious Trader\n* Sorkvild the Raven's Journal (2 Vol)\n* Sword Components\n* Blacksmithing Tools\n\n\n* New models and textures for all trees in Morrowind (models and textures by Vurt). Animations have been added to these trees and some bushes, meaning that leaves will sway in the wind.\n* New models for all rocks in Morrowind (models by Taddeus).\n* New models for Dwemer ruins/buildings and other statics as chairs, tables, lamps and pipes. Credit goes to \"ChampionOfHircine\".\n* New models for Telvanni buildings and other statics. Credits goes to \"ChampionOfHircine\".\n* Removed the \"multicolored\" and unnecessary second handle on Telvanni interior doors.\n* Fixes for a lot of issues with the Daedric tile-set. Thanks to \"Slartibartfast1\".\n* Fixes for a mesh error in Imperial Houses. Thanks to \"Kirlian Voyager\".\n* Golden Saints will have feminine walk instead of the default male one.\n* Included a fix for the misscolored armor pieces in the Ebony set.\n* Included a fix missing ring textures.\n* Included a fix for removing some gaps found in the Nordic broadsword/Claymore.\n* New optional main menu by \"Say\".\n* The Steel Broadsword now has its own mesh instead of sharing that of the Imperial Broadsword.\n* New Beast animations by Dirnae.\n* Improved/fixed mesh/meshes\n- Templar/Glass helmet (now fit orc heads)\n- Comberry ingredient\n- Nordic Mail Pauldron\n- Katanas/Dai-Katana/Tanto\n- Guarskin Drums\n- Dwemer Crossbow\n- Daedric War Axe\n- Daedric Key.\n- Dwemer weapons\n- Steel Crossbow\n- Iron weapons\n- Dwemer armor\n- Hackle-Lo\n- Mudcrab\n- Corpus Meat.\n- Heather\n- Scamp\n\nPost comment Comments\nPetrenko May 10 2013, 6:01am says:\n\nWow. What a decent changelog!\n\n+2 votes     reply to comment\ntrancemaster_1988 Author\ntrancemaster_1988 May 10 2013, 6:35am replied:\n\nAnd like i wrote in the summary..its not even close to the whole story.. :P\n\n+2 votes   reply to comment\nMetalspy May 10 2013, 6:28am says:\n\nI can't believe you're still working on this mod with such dedication. Amazing!\n\n+1 vote     reply to comment\nTheUnbeholden May 10 2013, 8:54am says:\n\nNew animations? Since when did you start doing this..\n\n+1 vote     reply to comment\ntrancemaster_1988 Author\ntrancemaster_1988 May 10 2013, 9:17am replied:\n\nI added the animations a long time ago. A lot of people doesn't know about the improved Beast animations by Dirnae and that's a shame since they're awesome. That's also why I included them in the first place.\n\n+2 votes   reply to comment\nTheUnbeholden May 15 2013, 6:07am replied:\n\nthat is always appreciated, animations. I think I've heard of new animations used for NPC's but I can't remember what it was called.\n\n+2 votes     reply to comment\nwikkid1 Jun 21 2013, 8:57pm says:\n\nWOW! I don't even know what else to say... this is great work! Like somebody said on another page, I'm going to have to dig out and re-install Morrowind just to experience this. Previously I never got past the first couple of hours... maybe now I'll be able to. (Never knew of TES until Oblivion GOTY came out, but that time Morrowind wasn't exactly new. Didn't get around to it until after Oblivion was like second home.)\n\n+1 vote     reply to comment\nGuest Jun 24 2014, 7:44am says:\n\n\ntrancemaster_1988 Author\ntrancemaster_1988 Jun 26 2014, 1:24pm replied:\n\nSoon TM\n\n+1 vote   reply to comment\nPost a Comment\nclick to sign in\n\n\n\nMorrowind Icon\nDeveloped By\nSend Message\nOfficial Page\nRelease Date\nReleased Mar 29, 2011\nMod Watch\nTrack this mod\nReport Abuse\nReport article\nRelated Mods\nMorrowind Rebirth 2.9 (Morrowind)\nMorrowind Rebirth 2.9 Morrowind - Single Player Role Playing\nRelated Games\nMorrowind Single Player First Person Shooter\n", "response": "# **Scene Concept: \"The Forgotten Armory of Outpost Renius\"**  \n\n## **1. Overall Scene Theme and Core Concept**  \nA recently rediscovered Imperial outpost, abandoned after a mysterious massacre, now serves as a hidden armory for a rogue faction of Legion deserters and smugglers. The environment is a **multi-level military storage complex**, filled with crated weapons, enchanted artifacts, and sealed magical hazards—requiring coordinated effort to navigate safely.  \n\n**Why Multi-Agent?**  \n- Heavy crates, locked doors, and trapped mechanisms demand **collaborative lifting, tool retrieval, and simultaneous actions**.  \n- The armory's **security systems** (shifting barricades, pressure plates) require **precise timing and role delegation**.  \n- **Hostile remnants** (animated armor, lingering magical wards) force agents to divide attention between combat, puzzle-solving, and item retrieval.  \n\n---  \n\n## **2. Spatial Layout and Key Areas**  \nThe outpost consists of three interconnected zones:  \n\n**A. The Barracks Entrance**  \n- A crumbling stone hallway with **collapsed rubble partially blocking the path**.  \n- **Wall-mounted torches flicker erratically** (some are illusionary—extinguishing them reveals hidden markings).  \n- A **broken ballista** leans against the wall, its crank mechanism rusted shut.  \n\n**B. The Central Armory**  \n- The primary storage chamber, dominated by **stacked iron-bound crates** (each labeled with Legion codes).  \n- A **raised officer’s platform** overlooks the room, accessible only by a **retractable ladder**.  \n- The floor has **pressure-activated tiles**—stepping on the wrong one triggers a ceiling net or alarm spell.  \n\n**C. The Vault of Cursed Relics**  \n- A magically sealed back room, its **door etched with Daedric runes**.  \n- Inside: **floating weapon racks**, a **cracked soul gem emitting a slow poison mist**, and a **caged Frost Atronach** (partially summoned, its icy limbs piercing the bars).  \n- A **large adamantium chest** (500 lbs, locked with a *temporal key* that must be retrieved from the barracks).  \n\n---  \n\n## **3. Detailed Area-by-Area Inventory**  \n\n### **A. The Barracks Entrance**  \n**a. Anchor Furniture & Installations:**  \n- A **overturned oak table** (splintered, one leg missing), barricading a side door.  \n- A **stone watchtower skeleton** (wooden steps rotten, metal rungs intact but loose).  \n\n**b. Key Interactive Objects:**  \n- **Legion Dispatch Ledger** (open to a page listing \"Contraband: 12 Silver Katanas\").  \n- **Rusted Ballista Crank** (requires oil + two agents to turn).  \n- **\"Borwen’s Lockbox\"** (small steel case, 8 lbs—locked, key hidden in a wall sconce).  \n\n**c. Functional Ambient Objects:**  \n- **Broken Training Dummies** (stuffed with moldy hay).  \n- **Oil Lanterns** (3/4 full, can be spilled to create slippery patches).  \n- **Legion Standard** (torn, draped over a weapon rack).  \n\n**d. Background Objects:**  \n- **Faded Recruitment Posters** (\"Enlist Today!\").  \n- **Discarded Dice Set** (scattered near a cot).  \n- **Bloodstain** (old, shaped like a dragged body).  \n\n---  \n\n### **B. The Central Armory**  \n**a. Anchor Furniture & Installations:**  \n- **Crated Weapon Stacks** (labeled \"DW-34\" [Dwemer Bolts], \"SL-22\" [Silver Longbows]).  \n- **Officer’s Desk** (mahogany, one drawer jammed shut).  \n\n**b. Key Interactive Objects:**  \n- **Pressure Tile Sequence** (stepping on the correct three tiles lowers the ladder).  \n- **\"Heartfang\" Battle Axe** (displayed on a rack—removing it triggers a blade trap).  \n- **Portable Campfire Kit** (unopened, can be assembled to melt ice barriers).  \n\n**c. Functional Ambient Objects:**  \n- **Barrel of Pickling Brine** (preserves discarded armor pieces).  \n- **Working Bell** (rings if the net trap is triggered).  \n- **Quill & Inkwell** (dried, but the quill is sharp enough to scratch runes).  \n\n**d. Background Objects:**  \n- **Dusty Legionnaire Helmet** (nesting a rat skeleton).  \n- **Scribbled Note** (\"Sjoring didn’t report back…\").  \n- **Nonfunctional Clock** (stuck at 3:47).  \n\n---  \n\n### **C. The Vault of Cursed Relics**  \n**a. Anchor Furniture & Installations:**  \n- **Daedric Ward Circle** (glowing faintly—stepping inside weakens the Atronach’s cage).  \n- **Floating Weapon Racks** (hover 4 ft off the ground, swaying slightly).  \n\n**b. Key Interactive Objects:**  \n- **Soul Gem Leak** (must be contained with a cloth + silver tongs).  \n- **Temporal Key** (shimmers in and out of existence; must be caught in a lead-lined box).  \n- **\"Voidguard\" Shield** (leans against the wall—attempting to lift it drains stamina).  \n\n**c. Functional Ambient Objects:**  \n- **Alchemy Set** (pestle cracked, mortar holds residue of Nether Salt).  \n- **Chain Pulley** (can lower the floating racks if weighted properly).  \n\n**d. Background Objects:**  \n- **Skull Carving** (\"Kagrenac was here\").  \n- **Frozen Corpse** (mid-scream, clutching a scroll).  \n- **Broken Dwemer Spider** (leg twitching intermittently).  \n\n---  \n\n## **4. Scene Affordances & Embedded Potential**  \n\n### **Collaborative Transportation Affordances:**  \n- **Adamantium Chest (500 lbs, 3m long)** – Requires two agents to lift, plus a third to stabilize the *Chain Pulley* lowering it from the vault.  \n- **Crated Dwemer Bolts (200 lbs, 1.5m tall)** – Labeled incorrectly; must be cross-referenced with the *Dispatch Ledger* before moving.  \n\n### **Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Task:**  \n  - *Identify the correct Silver Katana* among five near-identical racks:  \n    - **Unique Markers:** Only one has a *blue cloth wrap* + *\"Shat gro-Shazog\"* etched on the pommel.  \n    - **Distractor:** A *decorative blue glass dagger* nearby shares the color.  \n\n- **Compound Tool-Use Task:**  \n  - **Problem:** The *Frost Atronach’s cage* is failing.  \n  - **Solution Chain:**  \n    1. Use *Portable Campfire* to melt ice reinforcing the bars.  \n    2. Retrieve *Temporal Key* from barracks to lock the ward circle.  \n    3. Activate the *Chain Pulley* to drop a *Dwemer Coherer* (found in a crate) as a distraction.  \n\n---  \n\n**Final Atmosphere Notes:**  \n- The air smells of **ozone and iron**. Distant **clanks** suggest loose debris shifting.  \n- **Flickering torchlight** casts shadows that *briefly* form Daedric letters on the walls.  \n- A **half-visible spectral guard** patrols the vault—ignored unless directly confronted.  \n\nThis scene is a **dense playground** for collaborative problem-solving, where every object’s state and placement can pivot a task’s outcome."}