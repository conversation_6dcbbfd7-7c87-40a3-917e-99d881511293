{"id": 1404, "raw": "\nRetrieved from http://www.wired.com/2014/01/talkin-bout-app-store-revolution-enabling-monetizing-api-economy/\nText:\nSubscribe to WIRED Magazine\nInnovation Insights\n\n\n  • By <PERSON>, <PERSON><PERSON><PERSON><PERSON>  \n  • 12:22 pm  |  \n  • Permalink\nImage: <PERSON>. <PERSON>/Flickr\n\nImage: <PERSON>/Flickr\n\nWith very little capital requirements, the dotcom boom drastically shifted developers’ and innovators’ requirements to get applications to market, significantly reducing financial barriers to entry. According to Cutter Consortium, the number of private and public APIs has been growing exponentially in recent years and is projected to continue to rise. The number of open APIs is projected toreach 30,000 by 2016 (PDF document).\n\nFrom an application developer’s perspective, the API growth has been paramount within the last few years. However with this growth, comes a new approach that is moving away from the traditional app store model and transitioning into the “there’s an app for that” mentality. The proliferating growth of APIs has created another dimension into a world where we can tie APIs into platforms to pull out information and process capabilities.\n\nWhat Is the API Economy?\n\nIn the mid-90s, voicemail was the primary app for mobile phone service providers. People would call to check their voicemail, spending more airtime on their phones. That was an early version of enabling value added services on top of core offerings. Today, telcos have shifted to create their own services, and what they’ve discovered is that with the growth of cloud computing and open source, the number of application developers has accelerated; this acceleration has made it difficult for telcos to compete with large enterprises. The API economy is when traditional companies sell/provide more value added services on top of their core offerings.\n\nNow and Then\n\nWith the various open source components that are freely available to developers, the development lifecycle has been reduced dramatically. This is due to the following key factors:\n\n10 to 15 years ago: Open source trend was just getting started.\n\nToday: Some of the highest trafficked websites, such as Facebook, are entirely built on open source components. Now, in addition to not having to buy a lot of physical infrastructure to host applications, organizations no longer need to pay the licensing costs to embed a proprietary database into a product.\n\n10 to 15 years ago: Most people had a work computer locked down by IT, preventing risky downloadable activity. Work was done at work and when employees went home, the personal computing experience was different from that of the office.\n\nToday: BYOD came into the picture. IT isn’t just concerned with a single device but instead multiple devices. The typical employee, within large enterprises, brings their own laptop and smartphone and accesses the application through the network from computers at home. There is an always on and always connected mentality. This has driven the consumption of applications by users and created an application consuming economy.\n\nEmbracing the API Economy\n\nWhether it’s a bank, telco or electronics manufacturer, traditional companies are embracing independent developers. When organizations create their own core service, they can enable APIs, an environment and most importantly a business model that attracts developers to tie the application into their solution, in turn driving more usage, more exposure and ultimately more stickiness as users consume value added services (VARs).\n\nProviding an Incentive\n\nToday, there is a large focus on APIs in the enterprise. Whether it’s for users to check account balance or set minimum/maximum thresholds, banks are regularly enabled through an API. Despite this enablement, banks are realizing that in order to leverage application growth, they in turn need to leverage innovation from developers; providing them a financial incentive to tie in their apps into bank APIs. Ultimately, there are three different ways to drive this innovation:\n\n  • Drive consumption with complementary apps for established companies\n  • Expand usage of a company’s current application to drive initial growth (e.g. Twitter)\n  • Drive transactions within the applications (e.g. Netflix)\n\nCreating Revenue Opportunities\n\nThe average revenue per app for the entirety of its lifetime is somewhere between $1,000 and $4,000 if broken down, the average revenue per developer averages $6,000 for the Android marketplace and $21,000 for the Apple marketplace. While developers are recognizing that traditional app stores are great avenues of distribution, due to the lack of revenue opportunity in application development, developers are finding they need more avenues of distribution than traditional app stores typically provide.\n\nThe app development economy is indicative of its low barriers to entry that have enabled rapid innovation from an increasing number of developers. The API economy is increasing the amount of value that application developers can build out of their applications. The next step: the marketplace economy; which has the ability to replace the walled garden approach that we see with the current app store model.\n\nThe Who How and What of the API Economy\n\n  • Who are going to be these marketplaces?\n  • How will these marketplaces draw developers in?\n  • What will provide developers additional means to drive revenue?\n\nCompanies that are in traditional industries in mature markets, have the power to build out these marketplaces. Whether it is a payments provider, telco, bank, etc. traditional sectors have tremendous competition from standard players within the brick and mortar world all competing for similar types of customers segments. All things being equal, a small business will pick the bank that has the smallest fees. Banks are getting smarter about these hidden fees and overdraft charges and businesses are getting smarter about understanding these hidden fees. New business models, new entrants and people building applications over the cloud (e.g are taking market share away from existing customers of these traditional industries.\n\nThe questions that should be asked\n\n  • How can traditional companies provide their own app exchange?\n  • Where will they drive developers to want to build applications that tie into the company’s existing APIs?\n  • How do companies provide incentives for developers to develop to its API?\n  • How can one become a distribution channel for those developers?\n\nThe marketplace economy is driven by the business case shown here:\n\n  • Provide a services marketplace in addition to your core offering\n  • Whether it is business checking, CRM, on demand or a payment processing solution, provide a marketplace to enable developers to write new applications\n  • Provide a distribution conduit for those applications as a way to drive new sources of top line revenue and increase incentives for developers to add value to its core offering\n\nSo…Now What?\n\nOnce the traditional marketplace operator can bundle third party services with their existing core offerings, these traditional manufacturers can see additional customer profitability and differentiated core offerings without cutting on price, ultimately extending the brand to those who may not have visibility into the core service.\n\nFinally, it is a great way to create new channels. It is a two-sided business model: creating a marketplace and providing access to APIs so developers can come in and build services or value-added features. They will continue to sell through other app stores so they become, in turn, not only a developer but a channel partner of a company’s own core services.\n\nWhether name brand services that have been aggregated from well-known providers or innovative new services that have been developed to add value to offerings, a marketplace allows for catalog management and enables users to view services in the catalog to automatically get provisioned, get access to applications to have them downloaded to the device, and to handle all the security and administration.\n\nNow, customers have multiple value added services, around core offerings, that increase the satisfaction and usage of companies’ core services and drive additional revenue to the organization, as well as to developers. By enabling developers to develop, certify and distribute apps across a wide variety of device APIs, their customers are able to access a growing catalog of innovative apps, while driving a differentiated user experience, ultimately enabling a rich development and distribution ecosystem.\n\nSteve Crawford is Vice President of Marketing and Business Development at Jamcraker.\n\nOriginally posted by:\n", "response": "### **Scene Concept: \"The API Developer Co-Working Hub\"**  \n\n#### **1. Overall Scene Theme and Core Concept**  \nA bustling, modular co-working space tailored for independent API developers and small teams collaborating on digital marketplace integrations. The environment is a hybrid between a tech incubator and a hacker space, with multiple workstations, prototyping labs, and meeting pods—all designed to foster rapid innovation and API-driven product development.  \n\nThe scene is inherently multi-agent due to:  \n- **Shared Resources:** Limited high-end equipment (e.g., server racks, 3D printers) that requires coordination.  \n- **Distributed Knowledge:** Specialized tools and documents scattered across workstations, forcing developers to collaborate or barter access.  \n- **Time-Sensitive Tasks:** Debugging sessions, cloud deployment deadlines, and API certification checks that benefit from parallel workflows.  \n\n---\n\n### **2. Spatial Layout and Area Descriptions**  \nThe co-working hub is divided into four interconnected zones:  \n\n1. **Main Collaboration Floor** – Open-plan with adjustable desks, whiteboard walls, and a central \"API Dashboard\" displaying real-time integration metrics.  \n2. **Hardware Prototyping Lab** – A glass-walled side room with soldering stations, IoT testbeds, and a locked server cabinet.  \n3. **Quiet Pods & Meeting Nooks** – Semi-private booths for video calls or focused coding.  \n4. **Supply & Snack Corner** – A cluttered area with spare parts, energy drinks, and a malfunctioning vending machine.  \n\n---\n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **A. Main Collaboration Floor**  \n**a. Anchor Furniture & Installations:**  \n- A 4-meter-wide **API Status Board** (wall-mounted LCD array flickering with latency graphs).  \n- Six **adjustable-height workstations** (each with dual monitors, mechanical keyboards, and tangled USB hubs).  \n- A **mobile whiteboard cart** covered in half-erased API endpoint diagrams.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Certification Terminal\"** (an old kiosk running API validation tests; currently stuck on \"Error 429: Rate Limit Exceeded\").  \n- **Shared NFC Keycard** (grants access to the server lab; last seen tucked under a coffee-stained notebook).  \n- **Overloaded Power Strip** (daisy-chained extensions supporting three laptops, a phone charger, and a mini-fridge; buzzing ominously).  \n\n**c. Functional Ambient Objects:**  \n- **Noise-canceling headphones** (hanging on hooks; one pair has a frayed cable).  \n- **Label Maker** (out of tape; discarded \"WARNING: DEBUG MODE\" labels litter the floor).  \n- **Coffee Machine** (display reads \"Descale Needed\"; carafe half-full of stale brew).  \n\n**d. Background & Decorative Objects:**  \n- Peeling **\"API Best Practices\" poster** (corner torn where someone pinned a meme).  \n- **Sticky notes** (one reads \"FIX CORS!!!\" in red Sharpie).  \n- **Dead succulent** (in a mug labeled \"#1 DevOps Engineer\").  \n\n---\n\n#### **B. Hardware Prototyping Lab**  \n**a. Anchor Furniture & Installations:**  \n- **Locked Server Rack** (height: 2m; requires two people to lift safely; houses a raspberry Pi cluster).  \n- **Anti-static workbench** (strewn with loose resistors and a half-assembled sensor array).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Bricked\" IoT Device** (flashing red LED; USB debug cable dangling nearby).  \n- **Calibration Tool** (buried under a stack of datasheets; needed to fix the 3D printer).  \n\n**c. Functional Ambient Objects:**  \n- **Oscilloscope** (displaying erratic waveforms; probe tips scattered).  \n- **Fume Extractor** (turned off; filter clogged with solder residue).  \n\n**d. Background & Decorative Objects:**  \n- **Graffiti-style schematics** (drawn directly on the wall in dry-erase marker).  \n- **Pile of Rejected Prototypes** (a plastic bin labeled \"E-Waste??\").  \n\n---\n\n### **4. Scene Affordances and Embedded Potential**  \n\n#### **Collaborative Transportation Affordances:**  \n- **Server Rack (150kg, 2m tall)** – Requires two agents to relocate safely; one must stabilize while the other unplugs cables.  \n- **3D Printer Filament Spool (8kg, 60cm diameter)** – Too bulky for one person; needs a trolley or coordinated lift.  \n\n#### **Reasoning & Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:**  \n  - Among five **USB drives** on the desk:  \n    - *Target:* The only one with a **scratched \"API_KEYS_BACKUP\" label** and **missing cap**.  \n    - *Distractors:* One has a **Fortnite logo**, another is **encased in epoxy \"for art\"**.  \n- **Compound Reasoning:**  \n  - *Problem:* The **vending machine** is jammed (state: \"ERR: MOTOR STALL\").  \n  - *Solution:* The **maintenance toolkit** (with a **flathead screwdriver**) is in the supply closet, but the closet is locked.  \n  - *Key:* A **spare keycard** is hidden inside the **broken headphones' ear cushion**.  \n\n---\n\nThis environment is a playground for **collaborative problem-solving**, **emergent tool-use**, and **dynamic task allocation**—all while feeling like a lived-in, high-stakes developer hub."}