{"id": 1413, "raw": "\nRetrieved from http://dba.stackexchange.com/questions/15764/spatial-data-modelling-tools\nText:\nTake the 2-minute tour ×\n\nIs there anything like MySQL workbench for PostGIS? Something that supports SQL output and geometry types would be nice.\n\n\nI am trying to implement the Land Administration Domain Model and all I got are the specs ( UML diagrams). I need a tool that can create the models and output code that would create the tables. It needs to support geometry types as the LADM deals with parcels.\n\nshare|improve this question\n\n1 Answer 1\n\nYou could try GRASS or QGIS., open-source GIS applications. You might be able to get what you want from one of them.\n\nEDIT: I see you're after a data modelling tool with support for spatial data types. I can't see any specific reference with basic google-fu. You may be stuck with building your database model using CREATE TABLE statements and then reverse engineering them into a modelling tool for documentation.\n\nIf you have Visio Professional, the database modelling feature of that has a passably good database reverse engineering feature that will work OK with anything that supports an ODBC driver.\n\nAnother option might be to get an extensible tool such as Sparx Enterprise Architect and frig the metamodel. However, this might be more work than building your database by hand.\n\nshare|improve this answer\nAh no. What I want are tools for designing spatial database schemas. –  R.K<PERSON> Mar 29 '12 at 10:29\n@R.K. - you mean a data modelling tool that supports spatial data types? –  ConcernedOfTunbridgeWells Mar 29 '12 at 10:33\nYup. Preferably one that supports Postgresql/PostGIS as I'll most probably be using that database. Sorry for the ambiguity. Revised the question. –  R.K. Mar 29 '12 at 10:35\n@R.K. Maybe one of the more upmarket ones like PowerDesigner might do that, but it's quite pricey. –  ConcernedOfTunbridgeWells Mar 29 '12 at 10:37\n\nYour Answer\n\n\n\n", "response": "# **Scene Concept: Geospatial Data Modeling Lab**  \n\n## **Overall Scene Theme and Core Concept**  \nThis scene is set in a **Geospatial Data Modeling Lab**, a bustling workspace where urban planners, GIS specialists, and database engineers collaborate to design and manage land administration systems. The environment is inherently multi-agent due to:  \n- **Heavy computational equipment** requiring coordinated handling.  \n- **Complex spatial datasets** spread across multiple workstations.  \n- **Time-sensitive analysis tasks** demanding parallel processing.  \n\nThe lab is a hybrid of **high-tech digital workspaces and physical map archives**, with an emphasis on **precision, organization, and troubleshooting**. Collaboration is necessary due to the sheer volume of data and the need for cross-validation between digital models and physical records.  \n\n---  \n\n## **Spatial Layout and Area Descriptions**  \nThe lab is divided into **four interconnected zones**:  \n\n1. **Main Workbench Area** – The central hub where GIS analysts work on digital models. Cluttered with monitors, tablets, and scattered reference materials.  \n2. **Server & Storage Closet** – Houses the PostGIS database servers, backup drives, and physical map archives. Tight space requiring careful navigation.  \n3. **Collaboration & Whiteboard Zone** – A standing table with a large touchscreen for group discussions, surrounded by wall-mounted whiteboards filled with SQL queries and parcel diagrams.  \n4. **Equipment & Supply Corner** – Contains printers, scanners, and a small workshop for repairing field measurement tools.  \n\n---  \n\n## **Detailed Area-by-Area Inventory**  \n\n### **1. Main Workbench Area**  \n**a. Anchor Furniture & Installations:**  \n- A **6-meter-long L-shaped workstation** with **five ergonomic chairs**, each paired with a **27-inch 4K monitor** displaying different GIS software (QGIS, GRASS, PostGIS queries).  \n- A **large drafting table** tilted at 30° for manual parcel sketching, covered in **transparent grid overlay sheets**.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **PostGIS Server Terminal** – A dedicated workstation logged into the lab’s primary database, currently displaying an **unfinished SQL schema migration script** with syntax errors.  \n- **Digitizing Tablet** – A high-precision Wacom Cintiq device, stylus missing (last seen near the supply corner).  \n- **\"Priority\" Parcel Blueprint** – A large, rolled-up **cadastral map** (1.5m x 1m) labeled **\"LADM Pilot Project – DO NOT FOLD\"**, partially unrolled and weighted down by a coffee mug.  \n\n**c. Functional Ambient Objects:**  \n- **Labeled External Hard Drives** (3x, each **2TB**, with color-coded stickers: **\"RAW_SURVEY_DATA,\" \"PROCESSED_2024,\" \"BACKUP_A\"**).  \n- **Multi-function Printer** – Out of paper, flashing a **\"TRAY 2 EMPTY\"** warning.  \n- **Coffee Machine** – Half-full pot, lukewarm, surrounded by **three mismatched mugs** (one chipped, one with a \"World's Best GIS Analyst\" slogan).  \n\n**d. Background & Decorative Objects:**  \n- **Pinned Field Survey Photos** on a corkboard, some with handwritten notes like **\"Boundary dispute – verify coords!\"**  \n- **Stack of Old Atlases** (1980s editions) on a bottom shelf, gathering dust.  \n- **A broken desk fan** (blade bent) shoved into a corner.  \n\n---  \n\n### **2. Server & Storage Closet**  \n**a. Anchor Furniture & Installations:**  \n- **Rack-mounted PostGIS Server Cluster** (1.8m tall, 600kg total weight) with blinking status LEDs.  \n- **Industrial Shelving Unit** holding **archival map tubes** (each **1m long**, labeled with parcel IDs).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Backup Tape Drive** – Requires two people to lift (**25kg**, dimensions **60x40x30cm**) due to its awkward shape.  \n- **\"Master Parcel Registry\" Hard Drive** – A **5TB NAS** with a **bright red \"CRITICAL – DO NOT UNPLUG\"** label.  \n\n**c. Functional Ambient Objects:**  \n- **Labeled Cable Organizer** – A plastic bin filled with **ethernet, HDMI, and power cables**, some tangled.  \n- **Toolbox** – Contains **screwdrivers, crimpers, and spare server rack screws**.  \n\n**d. Background & Decorative Objects:**  \n- **Outdated Safety Poster** – \"Data Integrity Starts With You!\" (faded, slightly peeling at edges).  \n- **Dusty Topographic Globe** from the 1990s, missing a small chunk of Africa.  \n\n---  \n\n### **3. Collaboration & Whiteboard Zone**  \n**a. Anchor Furniture & Installations:**  \n- **Large Interactive Touchscreen** (86-inch, wall-mounted) currently frozen on a **PostGIS schema diagram**.  \n- **Whiteboard Wall** covered in **erasable marker notes**, including a half-erased **UML diagram** of the LADM.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Conference Phone** – Muted, with a **flashing \"CALL ON HOLD\"** light.  \n- **Abandoned Legal Document** – A **printed land deed** with a **sticky note: \"Verify against Plot 47-B!\"**  \n\n**c. Functional Ambient Objects:**  \n- **Wireless Keyboard & Mouse** – Battery low warning blinking.  \n- **Stool with Wheels** – One wheel squeaks when moved.  \n\n**d. Background & Decorative Objects:**  \n- **Dead Potted Plant** – A dried-up succulent in a cracked ceramic pot.  \n- **Framed Certificate** – \"Award for Excellence in Geospatial Innovation – 2018.\"  \n\n---  \n\n### **4. Equipment & Supply Corner**  \n**a. Anchor Furniture & Installations:**  \n- **Large Format Scanner** (needs calibration, **error code E-47** displayed).  \n- **Tool Repair Bench** – Cluttered with **disassembled GPS devices**.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Calibration Kit** – A **locked metal case** (key missing, last seen in the main workbench drawer).  \n- **Spare Digitizer Stylus** – Still in its **unopened packaging**, buried under a pile of **USB cables**.  \n\n**c. Functional Ambient Objects:**  \n- **Labeled Storage Bins** – \"Cables,\" \"Adapters,\" \"Spare Parts.\"  \n- **3D Printer** – Idle, with a **half-finished terrain model** on the build plate.  \n\n**d. Background & Decorative Objects:**  \n- **Coffee Stain** on the floor near the trash bin.  \n- **Stack of Old GIS Journals** (2015-2020 issues).  \n\n---  \n\n## **Scene Affordances and Embedded Potential**  \n\n### **Collaborative Transportation Affordances**  \n- **Server Rack Unit (600kg)** – Requires at least **two agents** to safely move due to weight and size.  \n- **Backup Tape Drive (25kg, bulky)** – Needs coordinated lifting to avoid dropping.  \n\n### **Reasoning and Tool-Use Affordances**  \n- **Attribute-based Reasoning:** Among **five nearly identical archival map tubes**, one is uniquely labeled **\"Parcel 47-B – CONFLICT ZONE\"** in red ink. A nearby **scanner error** suggests it must be cross-checked with digital records.  \n- **Compound Reasoning:** The **locked calibration kit** requires finding the **missing key**, which is hidden inside a **random USB cable bin**, forcing multi-step search and retrieval.  \n\n### **Atmospheric \"Noise\" for Realism**  \n- **Decorative blue glass paperweight** on the main desk could be mistaken for a **GIS sample** at a glance.  \n- **A humming HVAC vent** intermittently distracts from audio cues (e.g., server alarms).  \n\nThis **densely interactive** lab environment is primed for **complex multi-agent tasks**, blending **physical logistics, digital troubleshooting, and collaborative problem-solving**."}