{"id": 1425, "raw": "\nRetrieved from http://meta.stackexchange.com/questions/97799/why-is-down-vote-on-question-not-1/97800\nText:\nWhat is meta? ×\n\nDown voting a question gives -2 rep to the asker, but up voting gives +5.\n\nSince the up votes are halved on questions, why the down votes are not halved as well? Specially since they gives no penalty to the down voter.\n\nshare|improve this question\nWhy should it be proportional? –  <PERSON> the Lizard Jul 7 '11 at 19:49\n@<PERSON> I can see some logic there. If questions aren't important enough to award +10 per upvote, are they important enough to have the same penalty per downvote as answers? –  <PERSON> Jul 7 '11 at 19:55\n@<PERSON>: See my answer for my interpretation of the (somewhat arbitrary) numbers. –  <PERSON> the Lizard Jul 7 '11 at 19:58\npossible duplicate of Should a down vote on an answer be worth -4? –  <PERSON> Jul 8 '11 at 7:07\n@Code not really. He is asking -4 for answers –  <PERSON><PERSON><PERSON>ић Jul 8 '11 at 17:52\n\n1 Answer 1\n\nup vote 12 down vote accepted\n\nYou're talking about two different things. The +5 for a question upvote is a reward for asking a good question, but the -2 for a downvote is a penalty for asking a bad one. We want to encourage good answers more than good questions, so answer upvotes are worth more. But we want to discourage bad questions just as much, so the penalty for downvotes are the same.\n\nshare|improve this answer\nTechnically speaking, aren't bad questions discouraged more than bad answers, since there is no penalty on the person casting the downvote? –  <PERSON> 7 '11 at 20:06\nSmall problem with that is answers can be deleted, while questions can't (under certain circumstances), therefore the penalty for bad question can be severe. –  BЈовић Jul 7 '11 at 20:08\n@<PERSON>: It depends on how you look at it. People are more likely to use downvotes on questions, so they may be discouraged more often, but for each downvote they're discouraged by the same amount. Yet another way to look at it is proportionally (10/2 vs. 5/2), which gives you a stiffer penalty on question downvotes. On the whole, I'd say yes, technically bad questions are discouraged more than bad answers. I think that's okay though, since downvoted answers sink to the bottom of the page, while bad questions do not. –  Bill the Lizard Jul 7 '11 at 20:12\n@VJo: I don't see that as a problem. You can either edit a bad question to make it better (if it has a good answer, for example) or flag it for a moderator to delete. –  Bill the Lizard Jul 7 '11 at 20:14\n\nYou must log in to answer this question.\n\nNot the answer you're looking for? Browse other questions tagged .\n", "response": "# **Scene Concept: \"The Stack Exchange Moderator's Backroom\"**  \n\n## **1. Overall Scene Theme and Core Concept**  \nA cluttered, high-tech backroom for Stack Exchange moderators—part server hub, part debate chamber, part chaotic archive of unresolved user disputes. The room is a hive of digital governance, where upvotes and downvotes translate into physical consequences. The environment is inherently collaborative because moderators must work together to triage questions, adjust reputation scores, and resolve disputes—often requiring coordinated actions like moving heavy server racks, accessing locked terminals, or cross-referencing dense paper archives.  \n\n## **2. Spatial Layout and Area Descriptions**  \nThe room is divided into four interconnected zones:  \n\n- **The Reputation Adjustment Hub:** A central workstation with multiple monitors tracking live vote data.  \n- **The Server Farm:** A cramped, humming cluster of machines where digital reputations are physically adjusted via weighted reputation tokens.  \n- **The Debate Table:** A circular space with whiteboards, reference books, and scattered moderator notes.  \n- **The Archive Nook:** Floor-to-ceiling shelves packed with printed dispute logs, old policy manuals, and forgotten user petitions.  \n\n## **3. Detailed Area-by-Area Inventory**  \n\n### **A. The Reputation Adjustment Hub**  \n\n**a. Anchor Furniture & Installations:**  \n- A **triple-monitor workstation** (each screen flickering with different dashboards: vote analytics, flagged posts, moderator chat).  \n- A **heavy steel \"Reputation Vault\"** (a locked cabinet where physical reputation tokens are stored).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Upvote/Downvote\" Slider Console** (a mechanical control panel with two large levers—one labeled **\"+5\"**, the other **\"-2\"**—linked to the server farm).  \n- **A stack of weighted \"Reputation Tokens\"** (brass discs engraved with numbers: **+5**, **+10**, **-2**; some are missing).  \n- **A locked terminal** (requires two moderator keycards to override a disputed vote).  \n\n**c. Functional Ambient Objects:**  \n- **A label printer** (spitting out dispute tickets).  \n- **A coffee maker** (half-full, cold).  \n- **A wall-mounted analog clock** (stuck at 19:55, referencing the original post timestamp).  \n\n**d. Background & Decorative Objects:**  \n- **A faded \"Vote Fairly\" poster** (peeling at the edges).  \n- **A shelf of \"Moderator of the Month\" trophies** (dusty).  \n- **A crumpled sticky note** (scribbled with: *\"Why -2? Should be -1! – Anna\"*).  \n\n---  \n\n### **B. The Server Farm**  \n\n**a. Anchor Furniture & Installations:**  \n- **Four towering server racks** (humming loudly, LED indicators blinking erratically).  \n- **A large \"Reputation Redistribution Machine\"** (a pneumatic tube system that transports tokens to adjust scores).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **A jammed token dispenser** (a **+5 token lodged inside**, requiring two people to unstick).  \n- **A disconnected server node** (flashing red, labeled **\"Answer Penalty Module\"**).  \n\n**c. Functional Ambient Objects:**  \n- **A tool cart** (wrenches, screwdrivers, network testers).  \n- **A fire extinguisher** (expired last month).  \n\n**d. Background & Decorative Objects:**  \n- **Graffiti on the wall** (*\"Downvotes hurt more here.\"*).  \n- **A dead potted cactus** (symbolizing neglected questions).  \n\n---  \n\n### **C. The Debate Table**  \n\n**a. Anchor Furniture & Installations:**  \n- **A circular wooden table** (scratched from years of heated arguments).  \n- **A rolling whiteboard** (covered in equations comparing vote weights).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **A locked \"Policy Binder\"** (requires two keys to open).  \n- **A disputed post printout** (highlighted and annotated).  \n\n**c. Functional Ambient Objects:**  \n- **A projector** (showing a paused debate from 2011).  \n- **A half-empty water cooler** (leaking slightly).  \n\n**d. Background & Decorative Objects:**  \n- **A framed photo of \"Bill the Lizard\"** (a legendary moderator).  \n- **A stack of old Meta.SE magazines** (yellowed pages).  \n\n---  \n\n### **D. The Archive Nook**  \n\n**a. Anchor Furniture & Installations:**  \n- **Overstuffed bookshelves** (bending under the weight of dispute logs).  \n- **A rolling ladder** (squeaky wheels).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **A dusty \"2011 Downvote Reform\" dossier** (hidden behind other files).  \n- **A broken microfilm reader** (missing a lens).  \n\n**c. Functional Ambient Objects:**  \n- **A label maker** (out of tape).  \n- **A wheezing air purifier** (barely functional).  \n\n**d. Background & Decorative Objects:**  \n- **A framed \"First Downvote\" certificate** (tongue-in-cheek).  \n- **A mouse nest** (made of shredded policy drafts).  \n\n---  \n\n## **4. Scene Affordances and Embedded Potential**  \n\n### **Collaborative Transportation Affordances:**  \n- **The jammed token dispenser** (requires one agent to hold the release lever while another pries out the stuck **+5 token**).  \n- **The \"Reputation Vault\"** (weighs **200kg**, requires two agents to slide open).  \n\n### **Reasoning and Tool-Use Affordances:**  \n- **Identifying the correct policy binder** (among dozens, the right one has a **blue spine, a coffee stain, and a torn corner**).  \n- **Fixing the disconnected server node** (requires finding the **missing network cable**, which is coiled inside the tool cart beneath loose screwdrivers).  \n\nThis dense, atmospheric environment is primed for complex, multi-agent tasks—balancing digital governance with physical constraints, forcing moderators to collaborate, reason under clutter, and navigate bureaucratic absurdity."}