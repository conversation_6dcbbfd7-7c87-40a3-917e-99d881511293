{"id": 1434, "raw": "\nRetrieved from http://slashdot.org/story/06/04/05/1543233/osdl-to-bridge-gnome-and-kde\nText:\nPlease create an account to participate in the Slashdot moderation system\n\n\nForgot your password?\n\nOSDL to Bridge GNOME and KDE 321\n\nPosted by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\nfrom the one-api-to-rule-them-all dept.\nTrax8<PERSON> writes \"Open Source Development Labs is previewing work that will attempt to make life easier for software companies by bridging GNOME and KDE. The effort, called Portland Project, began showing its first software tools on in conjunction with this week's LinuxWorld Conference & Expo. Using them, a software company can write a single software package that works using either of the prevailing graphical interfaces. Working with on unifying interface issues, they plan to release a beta version of the software in May and version 1.0 in June. Ultimately, advocates hope that it will be part of a larger but separate effort called Linux Standard Base, which is designed to make the operating system easier for software companies to use.\"\n\nOSDL to Bridge GNOME and KDE\n\nComments Filter:\n  • by Anonymous Coward\n    Dammit, bridge for FVWM2 too!\n  • by WgT2 (591074) on Wednesday April 05, 2006 @01:07PM (#15068808) Journal\n\n    If it's no better than what Redhat did with their Frankenstein mix of Gnome and KDE, then I want nothing to do it.\n\n    I'd rather one or the other. But, really the other: KDE.\n\n    • WxWidgets (Score:2, Insightful)\n\n      by Anonymous Coward\n      why not use WxWidgets?\n      • by wysiwia (932559) on Wednesday April 05, 2006 @03:45PM (#15070481) Homepage\n        Because the real problem is not so much the used framework but to use a single set of guidelines. The main obstacle of the Linux desktop is the usability, the look&feel of the applications. If one just uses 2 different applications on Linux, one most likely has to learn 2 different ways how to work with. If one uses 10 different application one doesn't have to learn 10 different ways but quite possible 5 to 7.\n\n        So I created wyoGuide ( []) exactly for this, to finally have a single set of guidelines. And I designed wyoGuide to be cross-platform guidelines since no serious developer codes for a single platform these days. wyoGuide can and should be used on any platform with any framework and any language. Sure I do provide sample code written in C++ with wxWidgets but I'd love to put up others sample code as well. So far nobody familiar with other's framework volunteered.\n\n        To stress this point again, the Linux desktop won't become a success unless it can't be agreed on this single set of guidelines. It's possible that everybody sits together and designs yet another set but the outcome won't be much different than wyoGuide. On the other side wyoGuide is still work in progress and I'm open to any suggestion to make it more suitable for anybody.\n\n        If somebody doesn't believe me just read the LXer article here ( html []) and follow the links to the sources. Or go and read the guidelines themselves at .html [].\n\n        What I'm curious about is how the Portland project handles this info, the knew it since December 2005 ( s/2005-December/000349.html []), they seems to already have forgotten. I've also informed Novell and posted it to LinuxQuestions, almost no reaction. So what else can I do?\n\n        O. Wyss\n\n      At least that was a Red Hat thing. It's nice that OSDL is taking decisions that are automatically added to \"standards\" and that Linux is turning into a comitee-driven crap.\n  • by Giant Ape Skeleton (638834) on Wednesday April 05, 2006 @01:11PM (#15068848) Homepage\n    ...that the hybrid desktop will be gnown as Knome :)\n    • G.A.S.\n\n      Sorry, I couldn't resist...\n\n      But, gnown as Knome is REALLY one HAL of a good one... Imagine attaching Gnome to the HAL exoskeleton.... Oh, now THAT sentence was serendipitous...\n\n      Jeez... image word: chipmunk\n  • ask slashdot (Score:5, Interesting)\n\n    by Douglas Simmons (628988) on Wednesday April 05, 2006 @01:13PM (#15068878) Homepage\n    Slashdot ought to ask its visitors what their favorite features between the two that are not shared so this OSDL project can get more guidelines from the right demographic. Ask Slashdot is a powerful resource to collect knowledge, perhaps more than any other system in the galaxy.\n  • Merge ? (Score:2, Insightful)\n\n    by GrAfFiT (802657)\n    Why not just merge KDE and Gnome ?\n    I understand that my statement looks like a troll's dream but it would not be such a bad situation.\n    After all, Firefox is now the main F/OSS web browser with a large dominance among the F/OSS community. And it's not that bad. Why would it be so bad with desktop managers ?\n    Please enlighten me. Thank you.\n    • Re:Merge ? (Score:3, Interesting)\n\n      by chrismcdirty (677039)\n      Gnome is generally not configurable. KDE is configurable out the wazoo. That's why. Gnome seems to be very resource hungry. KDE has the option to run extremely light. That's why. Because I prefer KHTML/Konqueror to Gecko/Firefox. That's why.\n      • And more I forgot...\n\n        KDE is developed in C++/Qt. Gnome is developed in C/GTK. Two extremely different toolkits in separate languages. It'd probably be possible to port Gnome to C++/Qt, or KDE to C/GTK. But I know I wouldn't want to do that. Especially the KDE to C.\n      • Re:Merge ? (Score:3, Interesting)\n\n        by CastrTroy (595695)\n        I was a KDE user for a while. It was always slow and kludgy. I recently switched to Gnome on a whim, and I have to say It's about 10 times faster than KDE. What do you do to get KDE running faster than gnome?\n        • Install a version that's not badly outdated.\n        • drop all the eye candy (or should i say \"Kandy\"? har har har), such as animated menus, transparency, animated cursors, etc.\n\n          also, select a lightwheight theme instead of more bloated things like plastik or keramic.\n\n          the first time you run KDE (or after you rm -rf ~/.kde) it shows a wizard that allows you to select, between other things, the level of eye candy. just drag the slider all the way to the left to disable all gui effects.\n\n          or just do what i do. run windowmaker.\n          • I've done all that, and it still appears slow. Lag when typing in textboxes and lots of other problems. I know my computer is slow, and I know that Gnome is much faster than KDE, even when I disable everything on KDE, and leave gnome as default.\n    • Re:Merge ? (Score:5, Interesting)\n\n      by MAXOMENOS (9802) <maxomai&gmail,com> on Wednesday April 05, 2006 @01:35PM (#15069085) Homepage\n      What you suggest is a very difficult task.\n\n      The two desktop environments do basic tasks very differently. One of the main reasons why I no longer use Firefox on Linux is because I hate the GNOME file browser that Firefox uses by default. To me, all it does is make my job harder. For the sake of a more sensible file browsing interface, I am willing to tolerate Konqueror's relative slowness at loading web pages. Who's going to negotiate those differences?\n\n      The two desktop environments use very different core libraries with different licensing schemes (Qt is GPL, gtk is LGPL). These licensing schemes may carry big implications for those who use them (for example, you can base wxWindows on gtk without a problem, but can you do the same with wxWindows and Qt?)\n\n      There may also be major architectural differences that make a merging nontrivial.\n\n      Basically, what you're proposing is a huge project. The Portland Project has a much more limited scope, and I think it's much more achievable.\n\n      • Can you explain what Nautilus, the GNOME file manager, has to do with Firefox, a web browser? Your statment about GNOME and Firefox is nonsensical.\n        • Re:Merge ? (Score:5, Informative)\n\n          by tylers (666248) on Wednesday April 05, 2006 @02:04PM (#15069412) Homepage\n          His comment actually makes perfect sense. He speaks of the Firefox \"File -> Save Page As...\" and \"File -> Open File...\" dialogs, which are _really_ ugly and nonfunctional. Especially the Save Page As dialog. There is no way, for example, to see a list of files which includes sizes. The GIMP has the same ugly and nonfunctional save/open interfaces.\n\n          If I didn't like Firefox so much more than Konqueror, I'd switch myself. I hate the dialogs. The KDE versions are _much_ better, and I say this as a Fluxbox user who has spent a lot of time in both gnome and KDE.\n\n        • what he meant is the same thing i complained here []\n\n          mozilla foundation now is linking firefox and seamonkey against GTK2 by default, wich brings that awfull filechooser along with it.\n\n          most gnome apps use that anoying dialog because GTK2 is the foundation for gnome and it's apps. let me say that this filechooser is _THE_ main reason i stay away from GTK2 apps as much as i can.\n          • People complained about the GTK1 file dialog, people complain about the GTK2 file dialog, yet both of them are better then the horizontal scrolling Win32 style dialogs everyone and his dog uses.\n            • Re:Merge ? (Score:3, Insightful)\n\n              by kimvette (919543)\n              Actually, they're not; at least in Windows you can change the view from \"List\" to \"Details\" and see file attributes. Windows may have its bad points, but there are some things Microsoft really got right.\n\n              This brings me back to another post of mine in this thread [] and I'll say it again: In their quest to simplify the user experience, the Gnome developers have rendered the environment useless.\n          • Firefox->File->Open File.\n\n            Type in: /tmp/xxx\n\n            The GTK completes /tmp while you are typing, without completion overwrite,\n            so you end up with: /tmp/mp/xxx\n\n            If this manages to work the first time, backspace the whole thing, and you\n            will see the bug.\n\n            Or, try to type '/etc/fstab'\n            You get something like: /etc/tc/fstab\n            or better: /etc/termcapc/fstab.\n\n            I guess if you type poorly, this interface is for you.\n\n            Type a letter, look to see if it completed for you, type the next letter,...\n\n            The bug exists from RH9 to FC5.\n\n        Qt is triple-licensed, no? If you choose to license it under the QPL, you can probably create wrapper APIs that are LGPL-ed. The problem with KDE and pre-GPL'ed Qt is that the legality of GPL programs (KDE) linking against a QPL program is in question.\n      • Except for panels such as the file browser and print setup, and other popups like error messages and alerts, etc, people probably would never notice if they are running a Gnome or KDE program on either desktop. The differences in GUI between the toolkits are miniscule due to them copying each other and copying Windows (and Windows is only \"consistent\" because the toolkits there copy each other, there are in fact many *more* different GUI toolkits used on Windows than Linux).\n\n        I would like to see a Unix-style\n    • Re:Merge ? (Score:3, Informative)\n\n      by Kjella (173770)\n\n      Well, I'd say fairly different design philosophies. Using two completely different toolkits, written in two different lanuages. Actually, I think it's more the latter than the former. If you could incorporate both Gnome and KDE as a set of \"preferences\" of the same desktop manager, there'd at least be a lot less reason to argue about it.\n    • Re:Merge ? (Score:3, Insightful)\n\n      by sbrown123 (229895)\n      People don't want to merge. Why?\n\n      Well, some will tell you its good for competition. They are lieing.\n\n      Others will say why their desktop is superior because it has feature X. These people are just ignorant.\n\n      Some may even go into KDE being coded in C++ which is superior, or the reverse from Gnomers who think C is superior. These people need to go outside and get some sun, take a deep breath, and go find a real hobby.\n\n      The rest of us know the truth is that it doesn't really matter.\n    • Re:Merge ? (Score:3, Insightful)\n\n      by ElleyKitten (715519)\n      Why not just merge KDE and Gnome ?\n      Please enlighten me. Thank you.\n\n      Before there was Firefox, do you think that Opera and Mozilla should have merged? Would that have been a better solution than making Firefox?\n\n      Firefox has gained dominance beca\n    • Re:Merge ? (Score:3, Interesting)\n\n      by davidsyes (765062)\n\n      What I LIKE about FireFox is that it respects the Gnome code enough to let me use SCIM to input Kanji and other foreign characters into the search engine right in the browser. KDE/Konqueror WON'T let me, and it **appears** I have installed all the requisite stuff. The Gnome apps, even running in Konqueror DO let me use foreign characters. OO.o refuses to play ball, too.\n\n      What I DON'T like about Firefox is the lack of a Konqueror-like page archiver. I find myself copying or cutting the URL and pasting\n  • Remember Direct3D? (Score:5, Insightful)\n\n    by DaHat (247651) on Wednesday April 05, 2006 @01:13PM (#15068881) Homepage\n    Nice idea... of course like many I suspect I'm skeptical.\n\n    Look at the Windows side... Direct3D is pretty useful and was intended to remove the need for developers to write for specific graphics cards.\n\n    What happened? For a time everything was fine until the two major players, in an effort to differentiate themselves from the other went off in slightly different directions ultimately resulting in vanilla DirectX and Direct3D being a lowest common denominator between the two sides, and still forcing developers on both sides to write specific code for major devices so as to be able to offer the best experience.\n\n    I foresee a similar issue here. A common platform that enables an app written for it to work fine under KDE or Gnome will work great, at first, but then developers will find a feature of one or the other which they need, or at least want to have optional, so will design in parallel paths of UI rendering and functionality, ultimately resulting in a common framework that is insufficient for many apps.\n      Right, it certainly wasn't intended to keep developers away from OpenGL...\n  • But... (Score:5, Informative)\n\n    by tetabiate (55848) on Wednesday April 05, 2006 @01:15PM (#15068901)\n    The benevolent dictator said:\n\n    \"I personally just encourage people to switch to KDE.\n\n\n    Please, just tell people to use KDE.\"\n    • $0.02 (Score:3, Interesting)\n\n      by molarmass192 (608071)\n      I think GTK is admirable, but GNOME has regressed over the last 2-3 years to the point that it's no longer usable for me. The dumbing down of the GNOME widget set cornered me into a Fisher-Price user experience that I disliked greatly. Let's face it, I'm sure only a tiny tiny slice of Linux users are technophobes. Catering to such a tiny user base is a death wish for any but the most specialized of projects. If GNOME doesn't make an about face, it will eventually become nothing more than a fringe player wit\n      • It is funny, because Ubuntu, Redhat, and Novell (Suse) are all Gnome camps, so I would expect that KDE's share of the desktop pie will be rapidly decreasing.\n\n        I share your preference for KDE, but these companies need a desktop that they can unleash upon the masses and they have all concluded time and again that Gnome is the better desktop for this.\n\n    • by nizo (81281) *\n      Just out of curiosity, what functionality does Gnome lack that KDE has that one would need? I have been using X for over 12 years now, and seriously I couldn't give a flyingbuttmonkey [] if I used either Gnome or KDE; as long as it can pop open windows and is reasonably easy to configure however I want it to look; what else do I need?\n      • Re:But... (Score:3, Insightful)\n\n        Being able to have different wallpapers on different desktops might be nice.\n    • Re:But... (Score:5, Insightful)\n\n      by 19thNervousBreakdown (768619) <> on Wednesday April 05, 2006 @01:59PM (#15069353) Homepage\n\n      Then it's a good thing the benevolent dictator is in charge of the kernel and not the desktop environment. That kind of approach is great for an API, and if you have very technical users that don't mind spending hours setting things up OK for a desktop environment, but when you come right down to it most users either are idiots or want to behave like they are. That's not to say they're idiots in other areas; a rocket scientist could have problems using their computer, and there's no reason they should be an expert in both rocket science and desktop environments.\n\n      I don't consider myself an idiot, but I use Gnome and love it. It's not crippled in terms of functionality, but if an option doesn't really matter it's taken out, and if it does they put a lot of thought into making a sane and consistent way to use it. The environment not only gets out of my way, but helps me along to where I want to go.\n\n      Basically, just because he was responsible for the kernel doesn't make him qualified to make these decisions. I still like him, and he makes less asanine comments than most, this is nothing more than another addition to the list of asanine comments he's made.\n\n      • Re:But... (Score:4, Insightful)\n\n        by Senzei (791599) on Wednesday April 05, 2006 @03:20PM (#15070184)\n        The problem with that argument is that the options that don't really matter are different from person to person. KDE does not require you to spend hours tweaking the config, it does supply a default. If you don't like it, change it, but at that point you are not talking about the time required to get into a grunt-and-click state.\n    • All technical issues aside, I think a switch to KDE would pretty much kill Linux on the desktop, because all commercial Linux desktop applications would then depend on commercial software from Troll Tech.\n    • Simplicity has great value.\n\n      Have you read all 54,000 pages of tax code?\n\n      Are you aware of all the laws that apply to your daily life?\n\n      I believe simplicity in general, and especially simple laws and simple codes are important - otherwise you get to the point where not even one specialized person can understand a single entity.\n\n      I heartily applaud Gnome, Gaim, Firefox, and other open source projects who are making the effort to *simplify* their programs.\n\n      Simple is far from stupid; simple is smart!\n    • Re:But... (Score:3, Insightful)\n\n      by oddfox (685475)\n\n      How often do you deal with the computer illiterate and have to explain to them how to work their desktop correctly? Not often I'd wager because you'd realize that simplicity is the way to go, and power is being added onto the GNOME/GTK+ platform again as time goes on. The power is simply being placed where it should, within the confines of gconf where power users are free to alter the default behaviors at will, nevermind the options already present in the menus. KDE is overwhelming for the majority of users\n\n  • Cross platform? (Score:2, Interesting)\n\n    by gentimjs (930934)\n    Will this help users of non-linux systems, like myself running KDE on solaris/sparc whom are upset that all of Sun's bundled tools are gnome-specific and load up a billion gigs of dependant libraries when I try and launch them?\n  • This is not a new desktop. This is a layer of separation between developers and the underlying graphics libraries Qt (KDE) and GTK (Gnome). This is so I can code an app using this new API and it will run and look good on both KDE and GTK systems.\n  • I love the concept, I really hope that the implimentation will work out to be as good as the idea. If it works out this will be a major step towards bringing linux to more desktops.\n  • by eno2001 (527078) on Wednesday April 05, 2006 @01:17PM (#15068922) Homepage Journal\n    ...It's cement. (That's \"See mehnt\" for you Red staters) Geddit? Portland? Cement? Hahaha. Laugh. It's funny. Or something.\n  • Uh-oh. (Score:4, Funny)\n\n    by ClickOnThis (137803) on Wednesday April 05, 2006 @01:18PM (#15068932) Journal\n    From the article:\n\n    Portland Project is working on two ways to gloss over the differences\n\n    I hope this doesn't mean it's doomed from the start.\n  • by TheCoders (955280) on Wednesday April 05, 2006 @01:20PM (#15068956) Homepage\n    It's hard to tell exactly what this project is going to deliver, but it looks to me like an abstraction layer that will run on top of whatever GUI toolkit is available, rendering with native widgets.\n\n    This has been attempted before, and it usually doesn't catch on. There are plusses and minuses to both toolkits (as there are in any GUI toolkit). The problem that arises when you try to combine them is you end up with a superset of the negatives and none of the plusses that would lead you to choose one over the other. Essentially, it's the \"lowest common denominator\" problem. If a certain feature is present in one toolkit but not the other, then guess what? It's not going to make it into DAPI. If similar tasks are accomplished differently in the two toolkits, the Portland project is going to have to choose one, and shoehorn the other to fit. Either that, or introduce a third way of doing the same thing.\n\n    People view the existence of two competing desktop standards a \"problem.\" I disagree with that. As a developer, if I see a certain application already exists on my platform of choice, I'm not going to make another one, even if mine would have been better. On the other hand, if I were a KDE man, and there was an existing app for Gnome, but one that I didn't really like, then there's a little more incentive to make a native KDE version, in the mold of what I really want. In the end, it's the users who win, because they can pick and choose between both apps.\n\n    So for now, pick one and go with it. Don't fall into the trap of trying to conquer both worlds at once.\n  • by anandpur (303114) on Wednesday April 05, 2006 @01:23PM (#15068977)\n    Hi all,\n\n    Please consider this email a formal request from the GNOME Foundation.\n\n    We, being the GNOME Foundation, as well as many GNOME Foundation members and\n    contributors to the project, have contacted you numerous times over the last\n    four years regarding the use of the old GNOME logo on Slashdot. We've posted\n    comments on Slashdot stories covering GNOME. We've been very nice about it.\n\n    Please update the icon used for GNOME stories on Slashdot. We have used this\n    logo since 2002, when GNOME 2.0 was released. It has been a *very long* time\n    since the marble foot logo represented our project. We're now at GNOME 2.14,\n    so we've shipped seven releases since the new logo was adopted. In that time\n    you have posted over 120 articles in the GNOME category on Slashdot.\n\n    We'd really appreciate it if you updated the icon. It may not be a big deal\n    to you guys, but our logo is a mark of pride for our project. We'd like to\n    see it used.\n\n\n    - Jeff\n\n    From: 6-March/msg00002.html [] [] []\n  • and has been for years.\n  • by Teclis (772299) on Wednesday April 05, 2006 @01:30PM (#15069044) Homepage\n    Just what I needed. ANOTHER computing standard to learn. Which standard is next to join the act? Maybe the next one will be more standard than all the other standards.\n  • What's Next (Score:5, Funny)\n\n    by Sexy Commando (612371) on Wednesday April 05, 2006 @01:31PM (#15069057) Journal\n    vi and emacs?\n  • look *and* feel (Score:5, Interesting)\n\n    by eddeye (85134) on Wednesday April 05, 2006 @01:34PM (#15069072)\n\n    There's a difference between looks like kde and works like kde. Will the menus/config/keybindings be in the right place/format? Will the application handle dcop messages properly? Cross-platform toolkits usually abstract away the differences between platforms. It might translate the function calls and provide the right look, but that's only half of getting the proper look-and-feel.\n\n    The ubuntu openoffice-kde package does a nice job, but it's obviously not a kde application. I hope this toolkit gets it right because I would kill for a KDE version of firefox (damn these infernal gnome save dialogs!).\n\n  • So instead of a bunch of apps with names that start with \"k\" and a bunch of apps that start with the letter \"g\" we'll have a bunch of similar apps that start with the letter \"p!\"\n\n    It's crazy but it might just work!\n  • by phoenix.bam! (642635) on Wednesday April 05, 2006 @01:40PM (#15069136)\n    I'll tell you why I saw the light.. I was using Ubuntu with it's Gnome desktop.\n    Gnome was doing me well until I wanted to change something and couldn't. (Window manager metacity blows) So i switched to KDE's window manager, kwin.\n\n    Then one day I realized I liked Amarok and digiKam so I installed Kubuntu Desktop via apt-get while using Ubuntu. Figured I'd give KDE a try.\n\n    Within an hour I had KDE configured to look exactly like my gnome desktop, to every last button and taskbar. Then I realized, I didn't have to make it like gnome at all!\n\n    So in summary. KDE Is better than GNOME because KDE can look like GNOME but GNOME cannot look like KDE. Same as all squares are rectangles but not all rectangles are squares. Gnome is a square.\n\n    Also, i had a preconcieved notion that KDE was a Windows desktop clone, which it might be at first glance, but you can quickly and easily make it your own.\n\n    Gnome is just inferior in comparison, but I still run it on my laptop.\n    • >Within an hour I had KDE configured to look exactly like my gnome desktop\n\n      I suspect that this 'look alike' is rather shallow: Gnome use word as buttons actions while KDE tend to use OK/Cancel (a pity I like better KDE but not this part), I doubt that you could change that..\n    • The difference is this:\n\n      Gnome has realized that 99% of users NEVER CHANGE THE DEFAULTS. Slashdotters included; why do you think so many people complain about the paperclip and auto-format in Word instead of just spending 5 seconds turning those features off? Because they don't change the defaults; very few do.\n\n      So the key, the number one most important thing is that you must have everything working and looking good by default.\n\n      Everyone who loves KDE always mentions that they love it after spending an hour twi\n  • What it does (Score:4, Informative)\n\n    by Kelson (129150) * on Wednesday April 05, 2006 @01:52PM (#15069271) Homepage Journal\n    I've been running KDE apps on GNOME and vice-versa for years, largely thanks to the work of at getting them to use common drag-n-drop, system menus, and notification area. So based on the incredible lack of information in the article, I had to wonder... WTF does this do that isn't already possible?\n\n    The Portland project page isn't particularly informative either -- the description is too low-level: \"we're going to create two interfaces.\" OK, two interfaces to do what?\n\n    The Integration Tasks [] page actually provides information about what kinds of things they want to do: make sure apps built for both desktops will talk to the screen saver in the same way, deal with power management, share preferences like default apps, etc.\n\n    Sounds like a logical continuation of's efforts so far, and something that will improve matters for people like me who like some apps from one desktop and some from the other.\n    • re: WTF does this do that isn't already possible?\n\n      Maybe it will:\n      - give you buggy file/open dialogues\n      - change all verbose and understandable confirmations to read \"foo?\" [ok] [cancel]\n      - remove all of the configurability of kwin and add in all of the limitations of metacity\n      - remove styles and themes which have any hint of color, in favor of the \"corporate 2:00pm eyestrain\" look\n\n      All this and more will give the genuine Gnome experience to KDE users.\n\n      Sorry to be so negative, it's just\n        Aren't you confusing GNOME and Windows?\n  • I've always envisioned a perfect world. Where the libraries and such for each operating system would be part of a publicly avialable set, so that you could make a piece of software for one OS, and it would work on the others. Kind of like java, but at a level that would be implemented in C. Too bad this will never happen with Microsoft. They have no reason to allow Linux/Mac usage to spread any further. Yes, Microsoft released .NET stuff for Linux, but as I recall, it's still rather limited and not rea\n  • Isn't that Linux desktop unification what [] is supposed to do?\n  • will i be able to use KDEs superior dialogs such as kdeprint, filechooser, etc. with GTK2/gnome apps ?\n\n    also, will interface items like toolbars look and work like KDEs ?\n  • by Tumbleweed (3706) * on Wednesday April 05, 2006 @02:45PM (#15069816)\n    Let me know when it also works with ncurses.\n  • by mallan (37663) on Wednesday April 05, 2006 @04:56PM (#15071213) Homepage\n    Can we just have the \"desktops\" agree to disagree and have a configuration option for standardized dialogs and button order? It is absolutely retarded to have one app on your system have Ok/Cancel and the other app have Cancel/Ok.\n\n      Personally, I prefer the KDE style because I use Windows at work and dual boot at home. Ok/Cancel is what I'm used to, and it makes more sense to me. If Gnome users prefer the Mac way of doing things, hey - that's great. But no matter what *desktop* a Linux user is using, they are going to be using a mix KDE *apps* AND Gnome *apps*. Can we *please* just have a configuration option that switches button order, file browser dialog style, etc. based on what the *user* wants?\n\n  • by Maljin Jolt (746064) on Wednesday April 05, 2006 @08:45PM (#15072614) Journal\n    There is no need for mixing KDE and Gnome alltogether.\n\n    As I already mentioned in another slashdot discussion some time ago, I run KDE on vt7 amd Gnome on vt8. (And Fluxbox on vt9 just for OpenGL 3D accelerated games but that's another story.)\n\n    Just try it: On KDE 3.5.x, click \"Switch User:Start New Session\" on K menu. You will get your favourite login manager running on a new terminal. Pick another deskop you have installed. Switch back and forth with Alt+Ctrl+F7,F8,F9... And don't forget you still have your framebuffer consoles on Alt+Ctrl+F1..F6.\n\n", "response": "### **Scene Concept: \"The Portland Project Software Integration Lab\"**  \n\n#### **1. Overall Scene Theme and Core Concept**  \nA high-tech **software integration lab** where developers from **KDE and GNOME factions** collaborate (or clash) over unifying the Linux desktop experience. The lab is a chaotic blend of **open-source idealism and technical stubbornness**, filled with both cutting-edge tools and legacy systems.  \n\n**Why Multi-Agent?**  \n- **Physical Collaboration:** Heavy server racks, bulky testing rigs, and fragile prototypes require teamwork to move or repair.  \n- **Knowledge Sharing:** Developers must cross-reference documentation, debug logs, and whiteboard schematics to resolve compatibility issues.  \n- **Tool Coordination:** Some tasks (e.g., compiling a hybrid build) require one agent to fetch components while another configures the environment.  \n\n---\n\n### **2. Spatial Layout and Area Descriptions**  \nThe lab is divided into **four interconnected zones**, each reflecting a facet of the integration challenge:  \n\n1. **The Bridge Terminal** – Central workstation cluster where developers test cross-desktop compatibility.  \n2. **The Legacy Archive** – Shelves of old hardware and deprecated toolkits, a graveyard of past unification attempts.  \n3. **The Debugging Pit** – A dimly lit corner with oscilloscopes, logic analyzers, and tangled cables for diagnosing deep system issues.  \n4. **The Argument Lounge** – A break area with a coffee machine, whiteboard covered in heated debates, and a \"GNOME vs. KDE\" scoreboard.  \n\n---\n\n### **3. Detailed Area-by-Area Inventory**  \n\n#### **Zone 1: The Bridge Terminal**  \n**a. Anchor Furniture & Installations:**  \n- **Main Testing Cluster:** Four 32-inch monitors mounted on adjustable arms, each displaying different DEs (KDE Plasma, GNOME Shell, XFCE, and a prototype \"Portland\" build).  \n- **Server Rack (1.8m tall, 300kg):** Houses benchmarking rigs with dual-boot configurations. Requires two people to safely move.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Prototype Build Station:** A custom Linux box with a **dual-GPU setup** (one for KDE/Qt, one for GNOME/GTK).  \n- **Debugging Console:** A terminal logged into a **failing CI/CD pipeline**, displaying `[ERROR] GTK-KDE symbol conflict`.  \n\n**c. Functional Ambient Objects:**  \n- **Labeled Component Bins:** Sorted by framework (Qt widgets, GTK3 modules, legacy GTK2 libs).  \n- **USB Hub (16-port):** Half the slots are occupied by dongles for different input devices (testing keyboard shortcuts).  \n\n**d. Background & Decorative Objects:**  \n- **Sticky Notes:** `\"DO NOT MERGE - Linus hates this\"`, `\"Fix D-Bus API by EOD\"`.  \n- **Mini Fridge:** Emptied except for a single **\"KDE vs. GNOME\" energy drink** (expired).  \n\n---\n\n#### **Zone 2: The Legacy Archive**  \n**a. Anchor Furniture & Installations:**  \n- **Dusty Server Stack (2m tall, unstable):** Old Sun workstations, a **CRT monitor running FVWM2**, and a **box labeled \"Red Hat Frankenstein Mix (2003)\"**.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **\"Failed Unification\" Binder:** Contains specs for past projects (`WxWidgets attempt`, `GNOME-KDE middleware (abandoned)`).  \n- **Broken Optical Drive:** Ejects randomly—task: diagnose if hardware or driver issue.  \n\n**c. Functional Ambient Objects:**  \n- **Spare Parts Drawers:** Labeled `\"Misc. GTK Themes\"`, `\"Abandoned Qt Widgets\"`.  \n- **Y2K-Era Keyboard:** Missing the `Ctrl` key (used as a prop in arguments).  \n\n**d. Background & Decorative Objects:**  \n- **Framed Photo:** A younger Richard Stallman glaring at a KDE logo.  \n- **\"Wall of Shame\"** with burned CDs labeled `\"KDE 1.0 - It Compiles!\"`.  \n\n---\n\n#### **Zone 3: The Debugging Pit**  \n**a. Anchor Furniture & Installations:**  \n- **Oscilloscope (20kg, needs calibration):** Traces signals between GTK/KDE event loops.  \n- **\"The Tangle\":** A nest of USB, serial, and Ethernet cables (some unplugged, some live).  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Core Dump Printout:** A **stack trace** from a segfault in `libportland.so`.  \n- **Thermal Camera:** Used to detect which desktop environment heats the CPU more.  \n\n**c. Functional Ambient Objects:**  \n- **Soldering Iron (cold, but plugged in):** For last-resort hardware hacks.  \n- **Signal Generator:** Emits a **440Hz tone** when KDE apps freeze (a debugging \"feature\").  \n\n**d. Background & Decorative Objects:**  \n- **Poster:** `\"Keep Calm and strace -f\"`.  \n- **Dented Metal Cup:** Holds **fuses labeled \"GNOME 2.x Patience\"**.  \n\n---\n\n#### **Zone 4: The Argument Lounge**  \n**a. Anchor Furniture & Installations:**  \n- **Whiteboard (covered in red/green markers):** Lists `\"Pros/Cons of Qt Licensing\"`, `\"Why Systemd Won’t Fix This\"`.  \n- **Coffee Machine (out of order):** Display reads `\"ERROR: No GNOME Keyring Detected\"`.  \n\n**b. Key Interactive & Task-Relevant Objects:**  \n- **Voting Box:** Labeled `\"Should We Just Use XFCE?\"` (empty, padlocked).  \n- **\"Truce Agreement\" Draft:** A half-written doc titled `\"Portland API v1.0 - No Flame Wars\"`.  \n\n**c. Functional Ambient Objects:**  \n- **Stale Donuts:** In a box labeled `\"Bribes from Canonical\"`.  \n- **Broken Chair:** One leg replaced with **stacked KDE 3.5 manuals**.  \n\n**d. Background & Decorative Objects:**  \n- **\"Desktop Environment War\" Diorama:** Action figures of **Tux (injured)**, a **GNOME foot**, and a **KDE gear** in battle.  \n- **\"Days Since Last Fork\" Counter:** Stuck at `0`.  \n\n---\n\n### **4. Scene Affordances and Embedded Potential**  \n\n#### **Collaborative Transportation Affordances:**  \n- **Server Rack (300kg, 1.8m tall):** Requires two agents to lift safely (one to stabilize, one to guide).  \n- **CRT Monitor (25kg, fragile glass):** Needs careful handling—risk of dropping if one agent slips.  \n\n#### **Reasoning and Tool-Use Affordances:**  \n- **Attribute-Based Reasoning:** Among **five USB drives** on the desk:  \n  - *Target:* `\"Portland Alpha Build\"` (blue casing, **scratched label**, 64GB).  \n  - *Distractors:* Three generic drives + one **fake \"Linux ISO\" drive (actually Windows 98 recovery)**.  \n- **Compound (Tool-Use) Reasoning:**  \n  - *Problem:* **Locked BIOS settings** on the test rig (requires jumper reset).  \n  - *Solution:* **Jumper kit** is inside the **Legacy Archive**, buried under a **stack of Slackware CDs**.  \n\n---\n\n### **Final Notes**  \nThis lab is a **powder keg of collaboration and conflict**, where agents must:  \n- **Physically cooperate** (moving heavy/fragile objects).  \n- **Debug collectively** (matching logs with hardware states).  \n- **Navigate politics** (deciding which DE's conventions to prioritize).  \n\nThe density ensures **no task is trivial**—even fetching coffee requires negotiating a **broken machine and competing preferences**."}