{"description": "A clandestine diplomatic safehouse serving as a neutral negotiation space during a tense geopolitical standoff, featuring multiple rooms with high-security and collaborative objects.", "rooms": [{"id": "main_negotiation_hall", "name": "Main Negotiation Hall", "properties": {"type": "negotiation", "soundproofed": true}, "connected_to_room_ids": ["west_delegation_room", "east_delegation_room", "mediators_office"]}, {"id": "west_delegation_room", "name": "West Delegation Room", "properties": {"type": "delegation", "faction": "A"}, "connected_to_room_ids": ["main_negotiation_hall", "security_hub"]}, {"id": "east_delegation_room", "name": "East Delegation Room", "properties": {"type": "delegation", "faction": "B"}, "connected_to_room_ids": ["main_negotiation_hall", "storage_logistics_room"]}, {"id": "mediators_office", "name": "Mediator's Office", "properties": {"type": "office", "neutral": true}, "connected_to_room_ids": ["main_negotiation_hall", "security_hub"]}, {"id": "security_hub", "name": "Security Hub", "properties": {"type": "security", "monitored": true}, "connected_to_room_ids": ["west_delegation_room", "mediators_office", "storage_logistics_room"]}, {"id": "storage_logistics_room", "name": "Storage & Logistics Room", "properties": {"type": "storage", "secured": true}, "connected_to_room_ids": ["east_delegation_room", "security_hub"]}], "objects": [{"id": "oak_negotiation_table_1", "name": "Oak Negotiation Table", "type": "FURNITURE", "location_id": "in:main_negotiation_hall", "properties": {"size": [4.0, 1.2, 0.8], "weight": 85.0, "is_container": true, "material": "oak", "condition": "scratched"}, "states": {"is_flat": false}}, {"id": "sealed_agreement_folio_1", "name": "Sealed Agreement Folio", "type": "ITEM", "location_id": "on:oak_negotiation_table_1", "properties": {"weight": 5.0, "material": "leather", "seal": "wax", "fragility": "high", "provides_abilities": []}, "states": {"is_open": false, "is_packed": false}}, {"id": "jammed_projector_1", "name": "Jammed Projector", "type": "FURNITURE", "location_id": "in:main_negotiation_hall", "properties": {"size": [0.5, 0.5, 0.3], "weight": 12.0, "is_container": false, "brand": "Sony", "model": "VPL-HW45ES"}, "states": {"is_broken": false, "is_connected": true}}, {"id": "water_pitcher_1", "name": "Water Pitcher", "type": "ITEM", "location_id": "on:oak_negotiation_table_1", "properties": {"weight": 1.5, "material": "glass", "capacity": "1.5L", "provides_abilities": []}, "states": {"is_hot": true}}, {"id": "glass_1", "name": "Glass", "type": "ITEM", "location_id": "on:oak_negotiation_table_1", "properties": {"weight": 0.2, "material": "glass", "condition": "cracked", "provides_abilities": []}, "states": {"is_sterile": false}}, {"id": "glass_2", "name": "Glass", "type": "ITEM", "location_id": "on:oak_negotiation_table_1", "properties": {"weight": 0.2, "material": "glass", "condition": "pristine", "provides_abilities": []}, "states": {"is_sterile": false}}, {"id": "wall_clock_1", "name": "Wall Clock", "type": "FURNITURE", "location_id": "in:main_negotiation_hall", "properties": {"size": [0.3, 0.3, 0.1], "weight": 2.0, "is_container": false, "brand": "<PERSON><PERSON>", "accuracy": "fast"}, "states": {"is_mounted": true}}, {"id": "document_cart_1", "name": "Document Cart", "type": "FURNITURE", "location_id": "in:main_negotiation_hall", "properties": {"size": [0.8, 0.5, 0.9], "weight": 15.0, "is_container": true, "wheels": "sticky"}, "states": {"is_loaded": false}}, {"id": "intercom_panel_1", "name": "Intercom Panel", "type": "FURNITURE", "location_id": "in:main_negotiation_hall", "properties": {"size": [0.2, 0.3, 0.1], "weight": 3.0, "is_container": false, "label": "Mediator Only"}, "states": {"is_connected": true}}, {"id": "un_flag_1", "name": "UN Flag", "type": "ITEM", "location_id": "in:main_negotiation_hall", "properties": {"weight": 1.0, "material": "polyester", "condition": "crooked", "provides_abilities": []}, "states": {"is_mounted": true}}, {"id": "heavy_steel_desk_1", "name": "Heavy Steel Desk", "type": "FURNITURE", "location_id": "in:west_delegation_room", "properties": {"size": [1.8, 0.9, 0.75], "weight": 120.0, "is_container": true, "material": "steel", "bolted": true}, "states": {"is_open": false}}, {"id": "secure_shredder_1", "name": "Secure Document Shredder", "type": "FURNITURE", "location_id": "in:west_delegation_room", "properties": {"size": [0.5, 0.3, 0.4], "weight": 25.0, "is_container": false, "brand": "<PERSON>es", "status": "overheating"}, "states": {"is_hot": true}}, {"id": "encrypted_laptop_1", "name": "Encrypted Laptop", "type": "ITEM", "location_id": "on:heavy_steel_desk_1", "properties": {"weight": 2.5, "brand": "Dell", "model": "Latitude 7420", "password_location": "sticky_note", "provides_abilities": []}, "states": {"is_paired": false, "is_synced": false, "is_uploaded": false}}, {"id": "map_disputed_borders_1", "name": "Map of Disputed Borders", "type": "ITEM", "location_id": "on:heavy_steel_desk_1", "properties": {"weight": 1.0, "material": "paper", "pins": "loose", "provides_abilities": []}, "states": {"is_flat": false}}, {"id": "sealed_evidence_envelope_1", "name": "Sealed Evidence Envelope", "type": "ITEM", "location_id": "on:heavy_steel_desk_1", "properties": {"weight": 0.3, "material": "paper", "label": "Eyes Only – Mediator", "provides_abilities": []}, "states": {"is_open": false}}, {"id": "coffee_machine_1", "name": "Coffee Machine", "type": "FURNITURE", "location_id": "in:west_delegation_room", "properties": {"size": [0.3, 0.2, 0.4], "weight": 8.0, "is_container": false, "brand": "<PERSON><PERSON><PERSON>", "water_status": "empty"}, "states": {"is_loaded": false}}, {"id": "white_noise_generator_1", "name": "White Noise Generator", "type": "FURNITURE", "location_id": "in:west_delegation_room", "properties": {"size": [0.2, 0.15, 0.1], "weight": 1.5, "is_container": false, "brand": "Marpac", "sound": "humming"}, "states": {"is_connected": true}}, {"id": "tactical_display_1", "name": "Tactical Display", "type": "FURNITURE", "location_id": "in:east_delegation_room", "properties": {"size": [1.5, 0.1, 1.0], "weight": 30.0, "is_container": false, "brand": "Samsung", "mount_type": "wall"}, "states": {"is_mounted": true, "is_connected": true}}, {"id": "secure_phone_booth_1", "name": "Secure Phone Booth", "type": "FURNITURE", "location_id": "in:east_delegation_room", "properties": {"size": [1.0, 1.0, 2.0], "weight": 90.0, "is_container": true, "material": "soundproof_glass", "lock_type": "electronic"}, "states": {"is_open": false}}, {"id": "mediators_desk_1", "name": "Mediator's Desk", "type": "FURNITURE", "location_id": "in:mediators_office", "properties": {"size": [1.5, 0.8, 0.75], "weight": 50.0, "is_container": true, "material": "mahogany", "drawers": 4}, "states": {"is_open": false}}, {"id": "maintenance_key_1", "name": "Maintenance Key", "type": "ITEM", "location_id": "in:mediators_desk_1", "properties": {"weight": 0.1, "material": "metal", "label": "Projector Access", "provides_abilities": []}, "states": {"is_packed": false}}, {"id": "server_rack_1", "name": "Server Rack", "type": "FURNITURE", "location_id": "in:security_hub", "properties": {"size": [2.0, 0.8, 1.8], "weight": 100.0, "is_container": false, "brand": "APC", "wheel_status": "stuck"}, "states": {"is_loaded": false}}, {"id": "weapons_cabinet_1", "name": "Weapons Cabinet", "type": "FURNITURE", "location_id": "in:security_hub", "properties": {"size": [1.2, 0.4, 1.8], "weight": 80.0, "is_container": true, "lock_type": "biometric"}, "states": {"is_open": false}}, {"id": "locked_crate_1", "name": "Locked <PERSON>", "type": "FURNITURE", "location_id": "in:storage_logistics_room", "properties": {"size": [1.0, 0.6, 0.6], "weight": 25.0, "is_container": true, "material": "wood", "lock_type": "combination"}, "states": {"is_open": false}}, {"id": "spare_bulb_1", "name": "<PERSON><PERSON>", "type": "ITEM", "location_id": "in:locked_crate_1", "properties": {"weight": 0.2, "type": "projector", "compatibility": "Sony VPL-HW45ES", "provides_abilities": []}, "states": {"is_packed": false}}, {"id": "diplomatic_pouch_1", "name": "Diplomatic Pouch", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 1.0, "material": "leather", "marker": "red_thread", "provides_abilities": []}, "states": {"is_zipped": false, "is_packed": false}}, {"id": "diplomatic_pouch_2", "name": "Diplomatic Pouch", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 1.0, "material": "leather", "marker": "none", "provides_abilities": []}, "states": {"is_zipped": false, "is_packed": false}}, {"id": "packing_materials_1", "name": "Packing Materials", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 2.0, "type": "bubble_wrap", "quantity": "large_roll", "provides_abilities": ["pack"]}, "states": {"is_folded": true}}, {"id": "roller_1", "name": "Roller", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 3.0, "material": "steel", "type": "heavy_duty", "provides_abilities": ["flatten"]}, "states": {"is_lowered": true}}, {"id": "sterilizer_1", "name": "Sterilizer", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 5.0, "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "UV", "provides_abilities": ["sterilize"]}, "states": {"is_loaded": false}}, {"id": "scale_1", "name": "Scale", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 4.0, "brand": "Ohaus", "capacity": "10kg", "provides_abilities": ["weigh"]}, "states": {"is_loaded": false}}, {"id": "brush_1", "name": "Brush", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 0.5, "material": "nylon", "handle": "wood", "provides_abilities": ["scrub"]}, "states": {"is_scrubbed": true}}, {"id": "cleaning_agent_1", "name": "Cleaning Agent", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 1.0, "type": "disinfectant", "brand": "Lysol", "provides_abilities": ["scrub"]}, "states": {"is_loaded": false}}, {"id": "refrigerator_1", "name": "Refrigerator", "type": "FURNITURE", "location_id": "in:storage_logistics_room", "properties": {"size": [0.8, 0.7, 1.7], "weight": 60.0, "is_container": true, "brand": "Whirlpool", "temperature": "4C"}, "states": {"is_open": false}}, {"id": "scraper_1", "name": "<PERSON><PERSON><PERSON>", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 0.3, "material": "metal", "edge": "sharp", "provides_abilities": ["scrape"]}, "states": {"is_scraped": false}}, {"id": "hammer_1", "name": "Hammer", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 1.2, "material": "steel", "handle": "rubber", "provides_abilities": ["damage"]}, "states": {"is_loaded": false}}, {"id": "network_router_1", "name": "Network Router", "type": "FURNITURE", "location_id": "in:security_hub", "properties": {"size": [0.3, 0.2, 0.1], "weight": 1.5, "is_container": false, "brand": "Cisco", "status": "active"}, "states": {"is_connected": true}}, {"id": "mounting_brackets_1", "name": "Mounting Brackets", "type": "ITEM", "location_id": "in:storage_logistics_room", "properties": {"weight": 0.8, "material": "aluminum", "type": "heavy_duty", "provides_abilities": ["mount"]}, "states": {"is_mounted": false}}], "abilities": ["damage", "disconnect", "flatten", "load", "mount", "open", "pack", "pair", "raise", "scrape", "scrub", "sterilize", "sync", "unfold", "weigh", "zip", "close"]}