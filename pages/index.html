<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <!-- Meta tags for social media banners, these should be filled in appropriatly as they are your "business card" -->
  <!-- Replace the content tag with appropriate information -->
  <meta name="description" content="DESCRIPTION META TAG">
  <meta property="og:title" content="SOCIAL MEDIA TITLE TAG"/>
  <meta property="og:description" content="SOCIAL MEDIA DESCRIPTION TAG TAG"/>
  <meta property="og:url" content="URL OF THE WEBSITE"/>
  <!-- Path to banner image, should be in the path listed below. Optimal dimenssions are 1200X630-->
  <meta property="og:image" content="static/image/your_banner_image.png" />
  <meta property="og:image:width" content="1200"/>
  <meta property="og:image:height" content="630"/>


  <meta name="twitter:title" content="TWITTER BANNER TITLE META TAG">
  <meta name="twitter:description" content="TWITTER BANNER DESCRIPTION META TAG">
  <!-- Path to banner image, should be in the path listed below. Optimal dimenssions are 1200X600-->
  <meta name="twitter:image" content="static/images/your_twitter_banner_image.png">
  <meta name="twitter:card" content="summary_large_image">
  <!-- Keywords for your paper to be indexed by-->
  <meta name="keywords" content="KEYWORDS SHOULD BE PLACED HERE">
  <meta name="viewport" content="width=device-width, initial-scale=1">


  <title>ViewSpatial-Bench: Evaluating Multi-perspective Spatial Localization in Vision-Language Models</title>
  <link rel="icon" type="image/x-icon" href="static/images/avatar.png">
  <link href="https://fonts.googleapis.com/css?family=Google+Sans|Noto+Sans|Castoro"
  rel="stylesheet">

  <link rel="stylesheet" href="static/css/bulma.min.css">
  <link rel="stylesheet" href="static/css/bulma-carousel.min.css">
  <link rel="stylesheet" href="static/css/bulma-slider.min.css">
  <link rel="stylesheet" href="static/css/fontawesome.all.min.css">
  <link rel="stylesheet"
  href="https://cdn.jsdelivr.net/gh/jpswalsh/academicons@1/css/academicons.min.css">
  <link rel="stylesheet" href="static/css/index.css">

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script src="https://documentcloud.adobe.com/view-sdk/main.js"></script>
  <script defer src="static/js/fontawesome.all.min.js"></script>
  <script src="static/js/bulma-carousel.min.js"></script>
  <script src="static/js/bulma-slider.min.js"></script>
  <script src="static/js/index.js"></script>
</head>

<!-- HTML Structure -->
<style>
        .spatial-carousel {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            margin: 0 auto;
            color: #333;
            background-color: white;
            max-width: 1000px; /* Ensure the entire carousel doesn't exceed max content width */
        }

        .spatial-carousel .main-container {
            display: flex;
            flex-direction: row;
            width: 100%;
            gap: 20px;
            align-items: stretch; /* Make containers stretch to fill height */
            min-height: 400px; /* Minimum height to ensure proper centering */
        }

        .spatial-carousel .button-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            width: 180px; /* Fixed width for button container */
            flex-shrink: 0;
            justify-content: center; /* Center buttons vertically */
            align-self: center; /* Center the entire button container */
        }

        .spatial-carousel .content-area {
            flex-grow: 1;
            max-width: calc(100% - 220px); /* Accommodate button width + gap */
        }

        .spatial-carousel .category-button {
            background-color: #d9eeff; /* 更浅的蓝色 */
            border: 2px solid #a0d0ff; /* 稍微深一点的蓝色边框 */
            border-radius: 50px;
            padding: 10px 15px;
            font-size: 1em;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
            width: 100%;
        }

        .spatial-carousel .category-button:hover {
            background-color: #c4e3ff; /* 略深的悬停状态 */
            transform: translateY(-2px);
        }

        .spatial-carousel .category-button.active {
            background-color: #a0d0ff; /* 激活状态，使用边框颜色作为背景 */
            color: #333;
        }

        /* Special styling for orientation and location buttons */
        .spatial-carousel .category-button.teal {
            background-color: #d8f7f0; /* 更浅的绿色 */
            border: 2px solid #a0e5d5; /* 稍微深一点的绿色边框 */
        }

        .spatial-carousel .category-button.teal:hover {
            background-color: #c4f0e6; /* 略深的悬停状态 */
        }

        .spatial-carousel .category-button.teal.active {
            background-color: #a0e5d5; /* 激活状态，使用边框颜色作为背景 */
            color: #333;
        }

        .spatial-carousel .content-section {
            display: none;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            width: 100%;
            box-sizing: border-box;
            transition: width 0.3s ease, height 0.3s ease;
        }

        .spatial-carousel .content-section.active {
            display: block;
            animation: fadeIn 0.4s ease;
        }

        .spatial-carousel .content-wrapper {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .spatial-carousel .question-container {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            width: 100%;
            box-sizing: border-box;
        }

        .spatial-carousel .question {
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .spatial-carousel .answer {
            margin-bottom: 10px;
            font-weight: 500;
        }

        .spatial-carousel .question-type {
            font-style: italic;
            color: #666;
        }

        .spatial-carousel .image-container {
            margin: 0 auto;
            text-align: center;
            max-width: 100%;
            overflow: hidden;
        }

        .spatial-carousel .image-container img {
            width: auto;
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            display: block;
            margin: 0 auto;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .spatial-carousel .main-container {
                flex-direction: column;
            }

            .spatial-carousel .button-container {
                width: 100%;
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
                margin-bottom: 20px;
            }

            .spatial-carousel .category-button {
                width: auto;
            }

            .spatial-carousel .content-area {
                max-width: 100%;
            }
        }
    </style>

<body>

  <nav class="navbar" role="navigation" aria-label="main navigation">
    <div class="navbar-brand">
      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>
    <div class="navbar-menu">
      <div class="navbar-start" style="flex-grow: 1; justify-content: center;">
        <a class="navbar-item" href="https://github.com/ZJU-REAL">
        <span class="icon">
            <i class="fab fa-github"></i>
        </span>
        </a>

        <div class="navbar-item has-dropdown is-hoverable">
          <a class="navbar-link">
            More Research
          </a>
          <div class="navbar-dropdown">
            <!-- 导航项将在这里动态加载 -->
          </div>
        </div>
      </div>
    </div>
  </nav>

<script>
    window.HELP_IMPROVE_VIDEOJS = false;

    async function loadNavItems() {
      try {
        const response = await fetch('https://zju-real.github.io/paper-meta-info/meta.csv');
        const csvText = await response.text();

        // 简单的CSV解析
        const rows = csvText.split('\n')
          .map(row => row.trim())
          .filter(row => row) // 移除空行
          .map(row => {
            const [name, url] = row.split(',').map(cell => cell.trim());
            return { name, url };
          });

        return rows;
      } catch (error) {
        console.error('Error loading navigation items:', error);
        return [];
      }
    }

    $(document).ready(async function () {
      // 加载导航项
      const navItems = await loadNavItems();
      const navDropdown = $('.navbar-dropdown');

      // 清空现有的导航项
      navDropdown.empty();

      // 添加新的导航项
      navItems.forEach(item => {
        const navItem = $('<a></a>')
          .addClass('navbar-item')
          .attr('href', item.url)
          .text(item.name);
        navDropdown.append(navItem);
      });

      // carousel初始化代码
      var options = {
        slidesToScroll: 1,
        slidesToShow: 1,
        loop: true,
        infinite: true,
        autoplay: true,
        autoplaySpeed: 5000,
      }

      var carousels = bulmaCarousel.attach('.carousel', options);
      bulmaSlider.attach();
    });
  </script>

  <section class="hero">
    <div class="hero-body">
      <div class="container is-max-desktop">
        <div class="columns is-centered">
          <div class="column has-text-centered">
            <h1 class="title is-1 publication-title">ViewSpatial-Bench: Evaluating Multi-perspective Spatial Localization in Vision-Language Models</h1>
            <div class="is-size-5 publication-authors">
              <!-- Paper authors -->
                              <span class="author-block">
                  <a href="mailto:<EMAIL>" target="_blank">Dingming Li</a><sup>1,2*</sup>,
                </span>
                <span class="author-block">
                  <a href="mailto:<EMAIL>" target="_blank">Hongxing Li</a><sup>1*</sup>,
                </span>
                <span class="author-block">
                  Zixuan Wang<sup>1</sup>,
                </span>
                <span class="author-block">
                  Yuchen Yan<sup>1</sup>,
                </span>
                <span class="author-block">
                  Hang Zhang<sup>1</sup>,
                </span>
                <span class="author-block">
                  Siqi Chen<sup>1</sup>,
                </span>
                <span class="author-block">
                  Guiyang Hou<sup>1</sup>,
                </span>
                <span class="author-block">
                  Shengpei Jiang<sup>3</sup>,
                </span>
                <span class="author-block">
                  Wenqi Zhang<sup>1</sup>,
                </span>
                <span class="author-block">
                  <a href="mailto:<EMAIL>" target="_blank">Yongliang Shen</a><sup>1†</sup>,
                </span>
                <span class="author-block">
                  Weiming Lu<sup>1</sup>,
                </span>
                <span class="author-block">
                  Yueting Zhuang<sup>1</sup>,
                </span>
              </div>

                  <div class="is-size-5 publication-authors">
                    <span class="author-block"><sup>1</sup>Zhejiang University,</span>
                    <span class="author-block"><sup>2</sup>University of Electronic Science and Technology of China,</span>
                    <span class="author-block"><sup>3</sup>The Chinese University of Hong Kong</span>
                    <br>
                    <span class="author-block">Preprint. Under review.</span>
                    <span class="eql-cntrb"><small><br><sup>*</sup>Equal Contribution, <sup>†</sup>Corresponding Author</small></span>
                  </div>

                  <div class="column has-text-centered">
                    <div class="publication-links">
                         <!-- Arxiv PDF link -->
                      <span class="link-block">
                        <a href="https://arxiv.org/pdf/2505.21500.pdf" target="_blank"
                        class="external-link button is-normal is-rounded is-dark">
                        <span class="icon">
                          <i class="fas fa-file-pdf"></i>
                        </span>
                        <span>Paper</span>
                      </a>
                    </span>

                  <!-- Github link -->
                  <span class="link-block">
                    <a href="https://github.com/ZJU-REAL/ViewSpatial-Bench" target="_blank"
                    class="external-link button is-normal is-rounded is-dark">
                    <span class="icon">
                      <i class="fab fa-github"></i>
                    </span>
                    <span>Code</span>
                  </a>
                </span>

                <!-- ArXiv abstract Link -->
                <span class="link-block">
                  <a href="https://arxiv.org/abs/2505.21500" target="_blank"
                  class="external-link button is-normal is-rounded is-dark">
                  <span class="icon">
                    <i class="ai ai-arxiv"></i>
                  </span>
                  <span>arXiv</span>

                <!-- Dataset -->
                  <span class="link-block">
                      <a href="https://huggingface.co/datasets/lidingm/ViewSpatial-Bench" target="_blank"
                         class="external-link button is-normal is-rounded is-dark">
                          <span class="icon" style="vertical-align: middle; font-size: 20px;">&#129303;</span>
                          <span style="vertical-align: middle;">Dataset</span>
                      </a>
                  </span>

                </a>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>




  <!-- Teaser video-->
  <section class="hero teaser">
    <div class="container is-max-desktop">
      <div class="hero-body">
        <p>
<img src="static/images/fig1-1.png" alt="Learning from model weights" class="blend-img-background center-image" style="max-width: 100%; height: auto;" />
        </p>
        <br>
        <p>
            This work presents a range of spatial localization tasks requiring reasoning from both camera-centric and human-centric perspectives, revealing the challenges visual-language models (VLMs) face in multi-viewpoint spatial understanding. Current VLMs are predominantly trained on image-text pairs from the web that lack explicit 3D spatial annotations, limiting their cross-perspective spatial reasoning capabilities. To address this gap, we introduce <b>ViewSpatial-Bench</b>, the first comprehensive benchmark for evaluating spatial localization abilities across camera and human viewpoints spanning five distinct task types. Our benchmark leverages an automated 3D orientation annotation pipeline that efficiently generates diverse, scalable image datasets with precise directional labels. Additionally, this pipeline enables the creation of spatially annotated training data, substantially enhancing VLMs' multi-view spatial reasoning and cross-perspective generalization abilities.</p>
      </div>
    </div>
  </section>
  <!-- End teaser video -->

<!-- Paper abstract -->
<section class="section hero is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered has-text-centered">
      <div class="column is-four-fifths">
        <h2 class="title is-3">Abstract</h2>
        <div class="content has-text-justified">
          <p>
            Vision-language models (VLMs) have demonstrated remarkable capabilities in understanding and reasoning about visual content, but significant challenges persist in tasks requiring cross-viewpoint understanding and spatial reasoning. We identify a critical limitation: current VLMs excel primarily at egocentric spatial reasoning (from the camera's perspective) but fail to generalize to allocentric viewpoints when required to adopt another entity's spatial frame of reference. We introduce ViewSpatial-Bench, the first comprehensive benchmark designed specifically for multi-viewpoint spatial localization recognition evaluation across five distinct task types, supported by an automated 3D annotation pipeline that generates precise directional labels. Comprehensive evaluation of diverse VLMs on ViewSpatial-Bench reveals a significant performance disparity: models demonstrate reasonable performance on camera-perspective tasks but exhibit reduced accuracy when reasoning from a human viewpoint. By fine-tuning VLMs on our multi-perspective spatial dataset, we achieve an overall performance improvement of 46.24% across tasks, highlighting the efficacy of our approach. Our work establishes a crucial benchmark for spatial intelligence in embodied AI systems and provides empirical evidence that modeling 3D spatial relationships enhances VLMs' corresponding spatial comprehension capabilities.</p>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- End paper abstract -->


<section class="section hero is-small">
  <div class="container is-max-desktop">
    <div class="columns is-centered">
      <div class="column is-full">
        <div class="content">
          <h2 class="title is-3">ViewSpatial-Bench construction pipeline</h2>
         <p>
              <img src="static/images/pipline.png" alt="Directional Weight Score" class="blend-img-background center-image" style="max-width: 100%; height: auto;" />
            </p>
            <p>
                ViewSpatial-Bench comprises five localization recognition tasks across two complementary perspective frameworks. From the camera perspective: (1) Object Relative Direction recognition(Cam-Rel. Dir.), which determines spatial relationships between objects directly from images; (2) Object View Orientation recognition(Cam-Obj. Oir.), which identifies the gaze direction of individuals relative to the camera from an egocentric viewpoint. These tasks evaluate VLMs' intuitive, egocentric spatial understanding abilities. From the human perspective: (3) Object Relative Direction recognition(Per-Rel. Dir.), which involves adopting the viewpoint of a character in the image to determine the spatial relationships of other objects from their perspective; (4) Object View Orientation recognition(Per-Obj. Oir.), which requires assuming the position of a character in the image to determine the direction of their gaze; (5) Scene Simulation Relative Direction recognition(Per-Sce. Sim.), which requires modeling oneself within a spatial scene across sequential frames to determine relative positions of other objects. These latter three tasks assess VLMs' abstract, perception-dependent spatial awareness while accommodating complex human pose variations and spatial information in embodied scenarios.            </p>
        </div>
      </div>
    </div>
  </div>
</section>

  <section class="hero is-small spatial-carousel">
    <div class="main-container">
        <!-- Category Navigation Buttons -->
        <div class="button-container">
            <button class="category-button" onclick="showSpatialCategory('Per-Sce. Sim.')">Per-Sce. Sim.</button>
            <button class="category-button teal" onclick="showSpatialCategory('Cam-Rel. Dir.')">Cam-Rel. Dir.</button>
            <button class="category-button teal" onclick="showSpatialCategory('Cam-Obj. Dir.')">Cam-Obj. Dir.</button>
            <button class="category-button" onclick="showSpatialCategory('Per-Rel. Dir.')">Per-Rel. Dir.</button>
            <button class="category-button" onclick="showSpatialCategory('Per-Obj. Dir.')">Per-Obj. Dir.</button>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Random Section -->
            <div id="spatial-Per-Sce. Sim.-section" class="content-section">
                <div class="content-wrapper">
                    <div class="question-container">
                        <div class="question">Question: Standing at desk, gazing at pillow, where should shelves be?</div>
                        <div class="answer">Choices:A. back-left   B. front-right   C. front-left   D. back-right<br>Answer: B. front-right</div>
                        <div class="question-type">Question type: Per-Sce. Sim.</div>
                    </div>
                    <div class="image-container">
                        <img src="static/images/task1.png" alt="Random spatial reasoning example" onload="adjustContainer(this)">
                    </div>
                </div>
            </div>

            <!-- Orientation Section -->
            <div id="spatial-Cam-Rel. Dir.-section" class="content-section">
                <div class="content-wrapper">
                    <div class="question-container">
                        <div class="question">Question: How is the chair positioned with respect to the pillow?</div>
                        <div class="answer">Choices:A. right   B. back   C. left   D. above<br>Answer: A. right</div>
                        <div class="question-type">Question type: Cam-Rel. Dir.</div>
                    </div>
                    <div class="image-container">
                        <img src="static/images/task2.png" alt="Orientation spatial reasoning example" onload="adjustContainer(this)">
                    </div>
                </div>
            </div>

            <!-- Location Section -->
            <div id="spatial-Cam-Obj. Dir.-section" class="content-section">
                <div class="content-wrapper">
                    <div class="question-container">
                        <div class="question">Question: Taking the camera lens as the front, what direction is the man looking toward?</div>
                        <div class="answer">Choices:A. back   B. back-right   C. left   D. front<br>Answer: C. left</div>
                        <div class="question-type">Question type: Cam-Obj. Dir.</div>
                    </div>
                    <div class="image-container">
                        <img src="static/images/task3.png" alt="Location spatial reasoning example" onload="adjustContainer(this)">
                    </div>
                </div>
            </div>

            <!-- Height Section -->
            <div id="spatial-Per-Rel. Dir.-section" class="content-section">
                <div class="content-wrapper">
                    <div class="question-container">
                        <div class="question">Question: From the perspective of the man in white clothes, where is the man in red clothes?</div>
                        <div class="answer">Choices:A. front   B. right   C. left   D. back-left<br>Answer: B. right</div>
                        <div class="question-type">Question type: Per-Rel. Dir.</div>
                    </div>
                    <div class="image-container">
                        <img src="static/images/task4.png" alt="Height spatial reasoning example" onload="adjustContainer(this)">
                    </div>
                </div>
            </div>

            <!-- Multi-Object Section -->
            <div id="spatial-Per-Obj. Dir.-section" class="content-section">
                <div class="content-wrapper">
                    <div class="question-container">
                        <div class="question">Question: As the man in black in the photo, in which direction are you facing?</div>
                        <div class="answer">Choices:A. back   B. left   C. front-right   D. back-right<br>Answer: B. left</div>
                        <div class="question-type">Question type: Per-Obj. Dir.</div>
                    </div>
                    <div class="image-container">
                        <img src="static/images/task5.png" alt="Multi-object spatial reasoning example" onload="adjustContainer(this)">
                    </div>
                </div>
            </div>
        </div>
    </div>


<script>
    function showSpatialCategory(category) {
        // Hide all content sections
        const sections = document.querySelectorAll('.spatial-carousel .content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // Deactivate all buttons
        const buttons = document.querySelectorAll('.spatial-carousel .category-button');
        buttons.forEach(button => {
            button.classList.remove('active');
        });

        // Show selected content section
        const selectedSection = document.getElementById('spatial-' + category + '-section');
        selectedSection.classList.add('active');

        // Activate selected button
        const categoryButtons = document.querySelectorAll('.spatial-carousel .category-button');
        categoryButtons.forEach(button => {
            if (button.textContent.toLowerCase() === category) {
                button.classList.add('active');
            }
        });

        // Re-adjust container after switching
        const activeImg = selectedSection.querySelector('img');
        if (activeImg.complete) {
            adjustContainer(activeImg);
        }
    }

    // Function to adjust container based on content
    function adjustContainer(img) {
        // Make sure content doesn't exceed the width of the content area
        const contentArea = document.querySelector('.content-area');
        if (!contentArea) return;

        const maxWidth = contentArea.offsetWidth;
        const contentSection = img.closest('.content-section');

        // Set max width according to container
        if (contentSection) {
            contentSection.style.maxWidth = maxWidth + 'px';
        }
    }

    // Run on page load
    document.addEventListener('DOMContentLoaded', function() {
        showSpatialCategory('Per-Rel. Dir.');

        // Add resize event listener
        window.addEventListener('resize', function() {
            const activeSection = document.querySelector('.content-section.active');
            if (activeSection) {
                const activeImg = activeSection.querySelector('img');
                if (activeImg) {
                    adjustContainer(activeImg);
                }
            }
        });
    });
    </script>
</section>
<!-- End image carousel -->





<section class="section hero is-small is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered">
      <div class="column is-full">
        <div class="content">
          <h2 class="title is-3">Multi-View Spatial Model</h2>
          <div class="level-set has-text-justified">
              <p>
                We present Multi-View Spatial Model (MVSM), developed to address limitations in perspective-dependent spatial reasoning in vision-language models. Following the ViewSpatial-Bench pipeline, we constructed a training dataset of ~43K diverse spatial relationship samples across five task categories, utilizing automated spatial annotations from ScanNet and MS-COCO data, supplemented with Spatial-MM for person-perspective tasks. Using consistent language templates and standardized directional classifications, we implemented a Multi-Perspective Fine-Tuning strategy on Qwen2.5-VL (3B) to enhance reasoning across different observational viewpoints. This approach enables MVSM to develop unified 3D spatial relationship representations that robustly support both camera and human perspective reasoning.
              </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>



<section class="section hero is-small">
  <div class="container is-max-desktop">
    <div class="columns is-centered">
      <div class="column is-full">
        <div class="content">
          <h2 class="title is-3">Zero-Shot Evaluation Performance</h2>
         <p>
              <img src="static/images/table.png" alt="Directional Weight Score" class="blend-img-background center-image" style="max-width: 100%; height: auto;" />
            </p>
            <p>
                  Accuracy comparison across multiple VLMs on camera and human perspective spatial tasks. Our Multi-View Spatial Model (MVSM) significantly outperforms all baseline models across all task categories, demonstrating the effectiveness of our multi-perspective spatial fine-tuning approach. These results reveal fundamental limitations in perspective-based spatial reasoning capabilities among current VLMs. Even powerful proprietary models like GPT-4o (34.98%) and Gemini-2.0-Flash (32.56%) perform only marginally above random chance (26.33%), confirming our hypothesis that standard VLMs struggle with perspective-dependent spatial reasoning despite their strong performance on other vision-language tasks.
            </p>
        </div>
      </div>
    </div>
  </div>
</section>



<!-- Image carousel -->
<section class="hero is-small">
  <div class="hero-body">
    <div class="container">
      <div id="results-carousel" class="carousel results-carousel">

      <div class="item">
        <!-- Your image here -->
        <img src="static/images/case1-1.png" alt="MY ALT TEXT"/>
        <h2 class="subtitle has-text-centered">
          <strong>ViewSpatial-Bench Examples (Part 1).</strong>
  Performance comparison of three models (<em>Qwen2.5-VL (3B)</em>, <em>GPT-4o</em>, and <em>MVSM</em>)
  on five spatial reasoning tasks from
  <span style="background-color: #e0f7fa; padding: 2px 4px; border-radius: 3px;">human perspective</span>
  and
  <span style="background-color: #e8f5e9; padding: 2px 4px; border-radius: 3px;">camera perspective</span>.

        </h2>

      </div>
       <div class="item">
        <!-- Your image here -->
        <img src="static/images/case2-1.png" alt="MY ALT TEXT"/>
        <h2 class="subtitle has-text-centered">
          <strong>ViewSpatial-Bench Examples (Part 2).</strong>
        </h2>
      </div>
      <div class="item">
        <!-- Your image here -->
        <img src="static/images/case3-1.png" alt="MY ALT TEXT"/>
        <h2 class="subtitle has-text-centered">
          <strong>ViewSpatial-Bench Examples (Part 3).</strong>
        </h2>
      </div>
  </div>
</div>
</div>
</section>
<!-- End image carousel -->





<!--BibTex citation -->
  <section class="section" id="BibTeX">
    <div class="container is-max-desktop content">
      <h2 class="title">BibTeX</h2>
      <pre><code>@misc{li2025viewspatialbenchevaluatingmultiperspectivespatial,
      title={ViewSpatial-Bench: Evaluating Multi-perspective Spatial Localization in Vision-Language Models},
      author={Dingming Li and Hongxing Li and Zixuan Wang and Yuchen Yan and Hang Zhang and Siqi Chen and Guiyang Hou and Shengpei Jiang and Wenqi Zhang and Yongliang Shen and Weiming Lu and Yueting Zhuang},
      year={2025},
      eprint={2505.21500},
      archivePrefix={arXiv},
      primaryClass={cs.CV},
      url={https://arxiv.org/abs/2505.21500},
}</code></pre>
    </div>
</section>
<!--End BibTex citation -->


  <footer class="footer">
  <div class="container">
    <div class="columns is-centered">
      <div class="column is-8">
        <div class="content">

          <p>
            This page was built using the <a href="https://github.com/eliahuhorwitz/Academic-project-page-template" target="_blank">Academic Project Page Template</a> which was adopted from the <a href="https://nerfies.github.io" target="_blank">Nerfies</a> project page.
            You are free to borrow the source code of this website, we just ask that you link back to this page in the footer. <br> This website is licensed under a <a rel="license"  href="http://creativecommons.org/licenses/by-sa/4.0/" target="_blank">Creative
            Commons Attribution-ShareAlike 4.0 International License</a>.
          </p>

        </div>
      </div>
    </div>
  </div>
</footer>

<!-- Statcounter tracking code -->
  
<!-- You can add a tracker to track page visits by creating an account at statcounter.com -->


  </body>
  </html>
